<?php

namespace app;

use app\exception\CdnException;
use app\model\Book;
use Exception;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\Handle;
use think\exception\HttpException;
use think\exception\HttpResponseException;
use think\exception\ValidateException;
use think\helper\Str;
use think\Response;
use Throwable;
use TopThinkCloud\Exception\ExceptionInterface;
use yunwuxin\auth\exception\AuthenticationException;
use yunwuxin\auth\exception\UnauthorizedHttpException;

/**
 * 应用异常处理类
 */
class ExceptionHandle extends Handle
{
    /**
     * 不需要记录信息（日志）的异常类列表
     * @var array
     */
    protected $ignoreReport = [
        HttpException::class,
        HttpResponseException::class,
        ModelNotFoundException::class,
        DataNotFoundException::class,
        ValidateException::class,
        CdnException::class,
    ];

    protected $showErrorMsg = [
        HttpException::class,
    ];

    /**
     * 记录异常信息（包括日志或者其它方式记录）
     *
     * @access public
     * @param Throwable $exception
     * @return void
     */
    public function report(Throwable $exception): void
    {
        // 使用内置的方式记录异常日志
        if (!$this->isIgnoreReport($exception)) {
            $data = [
                'file'    => $exception->getFile(),
                'line'    => $exception->getLine(),
                'message' => $this->getMessage($exception),
                'code'    => $this->getCode($exception),
            ];

            $log = $data['message'];
            $log .= PHP_EOL . $this->app->request->url(true);
            $log .= PHP_EOL . "{$data['file']}:{$data['line']}";

            if ($this->app->config->get('log.record_trace')) {
                $log .= PHP_EOL . $exception->getTraceAsString();
            }

            try {
                $this->app->log->record($log, 'error');
            } catch (Exception) {
            }
        }
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @access public
     * @param \app\Request $request
     * @param Throwable $e
     * @return Response
     */
    public function render($request, Throwable $e): Response
    {
        $url = $request->url();

        // 添加自定义异常处理机制
        switch (true) {
            case $e instanceof UnauthorizedHttpException:
                break;
            case $e instanceof AuthenticationException:
                if (Str::startsWith($url, '/admin/')) {
                    return json(['url' => (string) url('/-/user/login')])->code(401);
                } else {
                    if (!$request->isJson()) {
                        if ($request->isParked()) {
                            $space = $request->getSpace();
                            if ($space) {
                                if ($request->isConsole()) {
                                    return redirect('/-/user/login')->remember(true);
                                } elseif ($space->isSSOAvailable()) {
                                    return view('user/sso')->assign('space', $space);
                                }
                            }
                        } else {
                            return redirect('/-/user/login')->remember(true);
                        }
                    }
                }
                break;
            case $e instanceof CdnException:
                return response($e->getMessage(), 422);
            case $e instanceof ValidateException:
                $error = $e->getError();
                if (is_string($error)) {
                    return response($error, 422);
                } else {
                    return json($error, 422);
                }
            case $e instanceof ModelNotFoundException:
                $message = "数据不存在";
                switch ($e->getModel()) {
                    case Book::class:
                        $message = "文档不存在或已被删除";
                        break;
                }

                $e = new HttpException(404, $message, $e);
                break;
            case $e instanceof ExceptionInterface:
                $e = new HttpException($e->getCode(), $e->getMessage());
                break;
        }
        // 其他错误交给系统处理
        return parent::render($request, $e);
    }

    protected function renderHttpException(\think\Request $request, HttpException $e): Response
    {
        if (!$request->isJson()) {
            $status = $e->getStatusCode();

            if (in_array($status, [401, 403, 404])) {
                return view('error', code: $status)->header($e->getHeaders())->assign(['e' => $e]);
            }
        }
        return parent::renderHttpException($request, $e);
    }
}
