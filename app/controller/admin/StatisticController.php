<?php

namespace app\controller\admin;

use app\lib\Date;
use app\model\Book;
use app\model\User;
use think\db\Query;

class StatisticController extends Controller
{
    public function basic()
    {
        $user = [
            'total'     => User::count(),
            'yesterday' => User::whereBetween('create_time', [Date::yesterday(), Date::today()])->count(),
        ];

        $book = [
            'total'     => Book::count(),
            'yesterday' => Book::whereBetween('create_time', [Date::yesterday(), Date::today()])->count(),
        ];

        return json(compact('user', 'book'));
    }

    public function user($period = '30days')
    {
        return $this->getPeriodData(
            $period,
            User::field('COUNT(*) as value')
        );
    }

    /**
     * @param $period
     * @param Query|\think\Model $query
     * @param $field
     * @return array
     */
    protected function getPeriodData($period, $query, $field = 'create_time')
    {
        [$start, $end, $unit, $list] = get_period($period);

        $dateField = get_date_query($field, $unit);

        $data = $query
            ->field("{$dateField} as date")
            ->whereBetween($field, [$start, $end])
            ->group('date')
            ->order('date asc')
            ->select()
            ->append([])
            ->visible([]);

        return fill_data($list, $data);
    }
}
