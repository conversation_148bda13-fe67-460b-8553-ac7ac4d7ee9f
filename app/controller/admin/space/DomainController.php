<?php

namespace app\controller\admin\space;

use app\controller\admin\Controller;
use app\lib\Cdn;
use app\model\Domain;

class DomainController extends Controller
{
    public function index()
    {
        return Domain::with(['space.owner'])->order('dreqs desc,bytes desc')->paginate();
    }

    public function cache(Cdn $cdn, $id)
    {
        $domain = Domain::findOrFail($id);

        $data = $this->validate([
            'cache' => 'require|boolean',
        ]);

        $domain->save($data);

        //关闭时刷新缓存
        if (!$data['cache']) {
            $domain->purge();
        } else {
            $cdn->updateBrowserCache($domain);
        }
    }
}
