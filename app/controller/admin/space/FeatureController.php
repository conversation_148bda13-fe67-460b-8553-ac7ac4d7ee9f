<?php

namespace app\controller\admin\space;

use app\controller\admin\Controller;
use app\model\Space;

class FeatureController extends Controller
{
    public function index($id)
    {
        $space = Space::findOrFail($id);
        
        // 只有组织空间才能设置功能
        if (!$space->isOrg()) {
            abort(404, '只有组织空间才能设置功能');
        }

        return response([
            'model' => $space->getFeature('model', '')
        ]);
    }

    public function save($id)
    {
        $space = Space::findOrFail($id);
        
        // 只有组织空间才能设置功能
        if (!$space->isOrg()) {
            abort(404, '只有组织空间才能设置功能');
        }

        $data = $this->validate([
            'model' => 'string',
        ]);

        $space->setFeature('model', $data['model'] ?? '');
        $space->save();

        return response('设置保存成功');
    }
}
