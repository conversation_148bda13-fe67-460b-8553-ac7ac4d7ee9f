<?php

namespace app\controller\admin\space;

use app\controller\admin\Controller;
use app\lib\Date;
use app\lib\Moderation;
use app\model\Space;
use app\model\User;
use think\db\Query;

class IndexController extends Controller
{
    public function index()
    {
        if ($this->request->has('owner')) {
            $qs = $this->request->param('owner');

            $query = Space::hasWhere('owner', function (Query $query) use ($qs) {
                $query->whereLike('name', "%{$qs}%");
            })->order('id desc');
        } else {
            $query = Space::order('id desc');
        }

        $this->filterFields($query, [
            'plan'        => function ($query, $value) {
                switch ($value) {
                    case 'personal':
                        $query->where('owner_type', User::class);
                        break;
                    default:
                        $query->where('plan', $value);
                }
            },
            'expire_time' => function ($query, $value) {
                switch ($value) {
                    case 'expired':
                        $query->where('expire_time', '<', Date::now());
                        break;
                    case 'expiring':
                        $query->whereBetween('expire_time', [Date::now(), Date::now()->addMonth()]);
                        break;
                    case 'valid':
                        $query->where(function ($query) {
                            $query->where('expire_time', '>', Date::now()->addMonth())
                                ->whereOr('expire_time', null);
                        });
                        break;
                }
            },
        ]);

        return $query->with(['owner'])->paginate()->append(['owners'], true);
    }

    public function plan($id)
    {
        $space = Space::findOrFail($id);

        $data = $this->validate([
            'plan'        => 'require',
            'expire_time' => 'require',
            'size'        => 'require|number',
        ]);

        $space->save($data);
    }

    public function block($id)
    {
        $space = Space::findOrFail($id);

        $space->save([
            'block_time' => Date::now(),
        ]);
    }

    public function unblock($id)
    {
        $space = Space::findOrFail($id);

        $space->save([
            'block_time' => null,
        ]);
    }

    public function moderation(Moderation $moderation, $id, $action)
    {
        if ($moderation->isAvailable()) {
            $space = Space::findOrFail($id);

            $value = match ($action) {
                'on'    => 1,
                'off'   => 0,
                default => abort(404)
            };

            $space->save([
                'moderation' => $value,
            ]);
        }
    }

    public function preemptive($id, $action)
    {
        $space = Space::findOrFail($id);

        $value = match ($action) {
            'on'    => 1,
            'off'   => 0,
            default => abort(404)
        };

        $space->save([
            'preemptive' => $value,
        ]);
    }
}
