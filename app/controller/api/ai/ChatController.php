<?php

namespace app\controller\api\ai;

use app\controller\api\Controller;
use think\ai\Client;
use function think\swoole\helper\iterator;

class ChatController extends Controller
{

    public function completions(Client $client)
    {
        $this->authorized('ai', $this->space);

        if ($this->space->ai_tokens <= 0) {
            abort(403, '空间Tokens余额不足');
        }

        $params = $this->request->only(['model', 'messages', 'tools', 'thinking', 'temperature', 'user']);

        //$params['model'] = 'qwen-max';

        $result = $client->chat()->completions($params);

        $generator = function () use ($result) {
            foreach ($result as $data) {
                yield 'data: ' . json_encode($data) . "\n\n";
                if (!empty($data['usage'])) {
                    $this->space->consumeAiTokens($data['usage']['total_tokens']);
                }
            }

            yield "data: [DONE]\n\n";
        };

        $response = iterator($generator());

        return $response->header([
            'Content-Type'      => 'text/event-stream',
            'Cache-Control'     => 'no-cache, must-revalidate',
            'X-Accel-Buffering' => 'no',
        ]);
    }
}
