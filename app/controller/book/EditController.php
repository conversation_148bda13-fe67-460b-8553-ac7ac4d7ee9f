<?php

namespace app\controller\book;

use app\controller\BookController;
use app\exception\SpaceExpiredException;
use Firebase\JWT\JWT;

class EditController extends BookController
{

    public function index()
    {
        if ($this->space->isExpired()) {
            throw new SpaceExpiredException;
        }

        $this->authorized('write', $this->book);

        $view = view('book/edit');

        if ($this->book->status == 1) {
            if (is_saas()) {
                $channel = config('editor.channel') . "@{$this->space->hash_id}";
                $token   = config('editor.token');
            } else {
                $channel = "license#" . get_license_id();
                $token   = env('LICENSE');
            }

            $isAdmin = can($this->user, 'admin', $this->book);

            $view->assign([
                'url'     => url('/open', domain: config('editor.host')),
                'channel' => $channel,
                'token'   => JWT::encode([
                    'exp'         => time() + 60 * 5,
                    'iat'         => time(),
                    'host'        => $this->space->web_url,
                    'book'        => [
                        'id'       => $this->book->id,
                        'name'     => $this->book->name,
                        'url'      => $this->book->web_url,
                        'ssh_url'  => $this->book->ssh_url,
                        'lfs_url'  => $this->book->lfs_url,
                        'git_url'  => $this->book->git_url,
                        'metadata' => [
                            'pack_types' => $this->book->pack_types,
                            'pay_read'   => $this->book->pay_read,
                            'powered_by' => $this->book->space->powered_by,
                            'plan'       => $this->book->space->plan,
                            'preemptive' => $this->book->space->preemptive,
                        ],
                    ],
                    'user'        => [
                        'id'     => $this->user->id,
                        'name'   => $this->user->name,
                        'email'  => $this->user->email,
                        'avatar' => $this->user->avatar,
                        'token'  => JWT::encode([
                            'exp'   => time() + 60 * 60 * 24 * 3,
                            'iat'   => time(),
                            'user'  => $this->user->id,
                            'space' => $this->space->id,
                        ], config('app.token'), 'HS256'),
                    ],
                    'permissions' => [
                        'read'    => true,
                        'write'   => true,
                        'release' => $isAdmin || $this->book->write_release,
                        'admin'   => $isAdmin,
                    ],
                ], $token, 'HS256'),
            ]);
        }

        return $view;
    }
}
