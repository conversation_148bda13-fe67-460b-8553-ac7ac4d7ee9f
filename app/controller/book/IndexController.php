<?php

namespace app\controller\book;

use app\controller\BookController;
use app\event\BookDeleted;
use app\exception\SpaceExpiredException;
use app\job\InitBook;
use app\lib\book\Creator;
use app\lib\Date;
use app\lib\Hashids;
use app\model\AccessToken;
use app\model\Book;
use app\model\BookMember;
use app\model\Member;
use app\model\User;
use Symfony\Component\Filesystem\Path;
use think\Db;
use think\db\Query;
use think\Event;
use think\exception\HttpException;
use think\exception\ValidateException;
use think\Filesystem;
use yunwuxin\auth\middleware\Authorize;

class IndexController extends BookController
{
    protected function initialize()
    {
        parent::initialize();
        if ($this->book) {
            $this->middleware(Authorize::class, 'admin', $this->book)
                ->only('setting', 'slug', 'delete', 'retry', 'update', 'price');
            $this->middleware(Authorize::class, 'create', $this->space)->only('create', 'save');
        }
    }

    public function create()
    {
        if ($this->space->isExpired()) {
            throw new SpaceExpiredException;
        }
        return view('book/create');
    }

    public function setting()
    {
        return view('book/setting');
    }

    public function save()
    {
        $data = $this->validate([
            'name|文档名称'             => 'require',
            'visibility_level|可见范围' => 'number',
            'original_type'             => '',
            'original_path'             => function ($value, $data) {
                switch ($data['original_type'] ?? 'normal') {
                    case 'token':
                        if (!empty($value)) {
                            $accessToken = AccessToken::getByToken($value, Book::class, 'read_repository');
                            if (empty($accessToken)) {
                                return '无效的文档令牌';
                            }
                        }
                        $message = '请输入文档令牌';
                        break;
                    case 'git':
                        if (!empty($value)) {
                            if (!filter_var($value, FILTER_VALIDATE_URL, FILTER_FLAG_PATH_REQUIRED)) {
                                return 'Git仓库URL格式有误';
                            }
                        }
                        $message = '请输入Git仓库URL';
                        break;
                    case 'template':
                        if (!empty($value)) {
                            $book = $this->space->books()
                                ->userBooks($this->space, $this->user)
                                ->where('is_template', 1)
                                ->where('id', $value)->find();
                            if (empty($book)) {
                                return '模板不存在';
                            }
                        }
                        $message = '请选择文档模板';
                        break;
                }
                if (!empty($message) && empty($value)) {
                    return $message;
                }
                return true;
            },
        ]);

        $this->space->createBook($this->user, $data);

        return redirect('/-/workspace');
    }

    /**
     * 导入文档
     * @param Creator $creator
     * @return mixed
     */
    public function import(Creator $creator)
    {
        $data = $this->validate([
            'type'    => 'require',
            'action'  => 'require',
            'payload' => 'require',
        ]);

        $channel = $creator->original($data['type']);

        $channel->setSpace($this->space);
        $channel->setUser($this->user);

        return call_user_func_array([$channel, $data['action']], $data['payload']);
    }

    public function check()
    {
        if ($this->book->status == 0 || $this->book->status > 1) {
            return abort(449);
        }
    }

    public function retry()
    {
        if ($this->book->status == -1) {
            $this->book->save(['status' => 0]);
            queue(InitBook::class, $this->book->id, queue: 'book');
        }
    }

    public function update()
    {
        $data = $this->validate([
            'field' => 'require',
            'value' => 'require',
        ]);

        if (in_array($data['field'], ['pdf', 'epub', 'html', 'word'])) {
            if ($data['value']) {
                $types = [...$this->book->pack_types, $data['field']];
            } else {
                $types = array_diff($this->book->pack_types, [$data['field']]);
            }
            $this->book->save([
                'pack_types' => $types,
            ]);
        } else {
            $this->book->save([
                $data['field'] => $data['value'],
            ]);
        }

        return response('修改成功');
    }

    public function price()
    {
        $data = $this->validate([
            'price|价格' => 'require|float',
        ]);

        $this->book->save([
            'price' => $data['price'],
        ]);

        return response('修改成功');
    }

    public function slug(Db $db)
    {
        $data = $this->validate([
            'slug|路径' => 'slug',
        ]);

        if (empty($data['slug']) || $data['slug'] == $this->book->hash_id) {
            $this->book->save(['slug' => null]);
        } else {
            try {
                Hashids::decode($data['slug']);
                throw new ValidateException(['slug' => '路径格式不合规，请修改后提交']);
            } catch (HttpException) {
            }

            $exist = $db->name('book')
                ->where('space_id', $this->space->id)
                ->where('id', '<>', $this->book->id)
                ->where('slug', $data['slug'])
                ->where(function (Query $query) {
                    $query->whereTime('delete_time', '>', Date::now()->sub(7, 'days'), 'OR');
                    $query->whereNull('delete_time', 'OR');
                })->find();

            if ($exist) {
                throw new ValidateException(['slug' => '该路径已存在，请修改后提交']);
            }

            $this->book->save(['slug' => $data['slug']]);
        }

        return response('修改成功');
    }

    public function delete(Event $event)
    {
        if ($this->book->status == 1) {
            $this->book->delete();
        } else {
            $this->book->force()->delete();
        }
        $event->trigger(new BookDeleted($this->user, $this->book));
        return redirect('/-');
    }

    public function download(Filesystem $filesystem, $type)
    {
        $ext = Book::PACK_TYPES[$type];
        if ($ext) {
            $filename = "{$this->book->name}-{$this->book->release_time->format('mdHi')}{$ext}";
            $path     = Path::join($this->book->hash_path, $type, $this->book->sha . $ext);
            $disk     = $filesystem->disk('release');

            if ($disk->has($path)) {
                return \think\swoole\helper\download($disk->path($path), $filename);
            }
        }
        abort(404);
    }

    public function quit()
    {
        /** @var BookMember $member */
        $member = $this->book->members()->where([
            'member_id'   => $this->user->id,
            'member_type' => User::class,
        ])->findOrFail();

        if ($member->access_level == Member::MASTER && !$this->book->members()
                ->where('member_type', User::class)
                ->where('member_id', '<>', $this->user->id)
                ->where('access_level', Member::MASTER)
                ->find()) {
            throw new ValidateException('至少保留一个管理员');
        }

        $member->delete();

        return redirect('/-');
    }

    public function template()
    {
        $query = $this->space->books()
            ->where('is_template', 1)
            ->userBooks($this->space, $this->user);

        if ($this->request->has('value')) {
            $query->where('id', $this->request->param('value'));
        }

        $templates = $query->select();

        return json($templates->map(function (Book $book) {
            return [
                'text'  => $book->name,
                'value' => $book->id,
                'image' => $book->logo,
            ];
        }));
    }
}
