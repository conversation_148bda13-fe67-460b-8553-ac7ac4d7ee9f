<?php

namespace app\controller\book;

use app\controller\BookController;
use app\lib\goods\Book;

class PurchaseController extends BookController
{
    public function index()
    {
        $returnUrl = $this->request->param('redirect_to', (string) url($this->book->uri)->domain(true));

        if (can($this->user, 'content', $this->book)) {
            return redirect($returnUrl);
        }

        $book = new Book($this->book, $returnUrl);
        $book->setUser($this->user);
        $result = $book->purchase();

        return redirect($result['pay_url']);
    }
}
