<?php

namespace app\controller\book;

use app\controller\BookController;
use app\lib\Date;
use yunwuxin\auth\middleware\Authorize;

class SaleController extends BookController
{
    protected function initialize()
    {
        parent::initialize();
        $this->middleware(Authorize::class, 'admin', $this->book);
    }

    public function index()
    {
        $sales = $this->book->sales()->with(['user'])->order('id desc')->paginate();

        // 计算销售统计信息
        $statistics = $this->calculateStatistics();

        return view('book/sale')->assign([
            'sales' => $sales,
            'statistics' => $statistics,
        ]);
    }

    /**
     * 计算销售统计信息
     */
    private function calculateStatistics()
    {
        $salesQuery = $this->book->sales();

        // 总销售统计
        $totalSales = $salesQuery->count();
        $totalAmount = $salesQuery->sum('amount');

        // 今日销售统计
        $todayStart = Date::today();
        $todayEnd = Date::tomorrow();
        $todaySales = $this->book->sales()
            ->whereBetween('create_time', [$todayStart, $todayEnd])
            ->count();
        $todayAmount = $this->book->sales()
            ->whereBetween('create_time', [$todayStart, $todayEnd])
            ->sum('amount');

        // 本月销售统计
        $monthStart = Date::now()->startOfMonth();
        $monthEnd = Date::now()->endOfMonth();
        $monthSales = $this->book->sales()
            ->whereBetween('create_time', [$monthStart, $monthEnd])
            ->count();
        $monthAmount = $this->book->sales()
            ->whereBetween('create_time', [$monthStart, $monthEnd])
            ->sum('amount');

        return [
            'total_sales' => $totalSales,
            'total_amount' => $totalAmount,
            'today_sales' => $todaySales,
            'today_amount' => $todayAmount,
            'month_sales' => $monthSales,
            'month_amount' => $monthAmount,
        ];
    }

    public function delete($id)
    {
        /** @var \app\model\BookSale $sale */
        $sale = $this->book->sales()->findOrFail($id);

        if (!$sale->canRevoke()) {
            throw new \think\exception\ValidateException('该订单已超过7天，无法撤销');
        }

        $sale->ord->revoke();
    }
}
