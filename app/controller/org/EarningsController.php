<?php

namespace app\controller\org;

use app\lib\Date;
use app\model\OrganizationEarnings;

class EarningsController extends Controller
{
    public function index()
    {
        // 获取收入明细
        $earningses = $this->org->earningses()->order('id desc')->paginate();

        // 计算统计数据
        $statistics = $this->calculateStatistics();

        return view('org/earnings')->assign([
            'earningses' => $earningses,
            'statistics' => $statistics,
            'founder'    => $this->org->founder,
        ]);
    }

    /**
     * 计算收入统计数据
     */
    private function calculateStatistics()
    {
        // 本月收入：本月的收入总额（只计算收入，不包括结算）
        $monthStart    = Date::now()->startOfMonth();
        $monthEnd      = Date::now()->endOfMonth();
        $monthlyIncome = $this->org->earningses()
            ->where('type', OrganizationEarnings::TYPE_INC)
            ->whereBetween('create_time', [$monthStart, $monthEnd])
            ->sum('amount') ?: 0;

        // 累计收入：所有收入的总和（只计算收入，不包括结算）
        $totalIncome = $this->org->earningses()
            ->where('type', OrganizationEarnings::TYPE_INC)
            ->sum('amount') ?: 0;

        // 已结算：所有结算的总和
        $totalSettled = $this->org->earningses()
            ->where('type', OrganizationEarnings::TYPE_DEC)
            ->sum('amount') ?: 0;

        $balance = $totalIncome - $totalSettled;

        if ($balance !== $this->org->earnings) {
            //自动修正
            $this->org->save([
                'earnings' => $balance,
            ]);
        }

        return [
            'balance'        => $balance,                    // 当前余额（分）
            'monthly_income' => $monthlyIncome,       // 本月收入（分）
            'total_income'   => $totalIncome,           // 累计收入（分）
            'total_settled'  => $totalSettled,         // 已结算（分）
        ];
    }
}
