<?php

namespace app\controller\org\billing;

use app\controller\org\Controller;
use app\lib\Date;
use app\lib\goods\Plan;
use app\lib\goods\Upgrade;
use app\lib\plan\EnterprisePlan;

class PlanController extends Controller
{
    public function index($name)
    {
        $plan = $this->space->current_plan;
        if ($name == 'team' && $plan instanceof EnterprisePlan && !$plan->canDowngrade()) {
            abort(404);
        }
        $action = $plan->action($name);
        return view("org/billing/plan/{$action}")->assign('name', $name);
    }

    public function save($name)
    {
        $plan = $this->space->current_plan;

        if ($name == 'team' && $plan instanceof EnterprisePlan && !$plan->canDowngrade()) {
            abort(404);
        }

        $action = $plan->action($name);

        switch ($action) {
            case 'downgrade':
                $days = $this->space->expire_time->diffInDays(Date::now());

                $newDays = ceil($days * Plan::PRICES[$this->space->plan] / Plan::PRICES[$name]);

                $this->space->save([
                    'plan'        => 'team',
                    'expire_time' => Date::now()->addDays($newDays),
                ]);
                return redirect('/-/org/billing');
            case 'upgrade':
                $goods = new Upgrade($this->space, $name);
                break;
            case 'buy':
                $data = $this->validate([
                    'years' => 'require|in:1,2,3',
                ]);

                $goods = new Plan($this->space, $name, $data['years']);
                break;
            default:
                abort(404);
        }
        $goods->setUser($this->user);
        $result = $goods->purchase();
        return redirect($result['pay_url']);
    }
}
