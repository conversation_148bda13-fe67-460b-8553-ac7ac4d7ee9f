<?php

namespace app\controller\org\billing;

use app\controller\org\Controller;
use app\lib\Date;
use app\lib\goods\Size;

class SizeController extends Controller
{
    public function index()
    {
        $days = $this->space->expire_time->diffInDays(Date::now());
        return view('org/billing/size')->assign('days', $days);
    }

    public function save()
    {
        $data = $this->validate([
            'size|空间大小' => 'require|integer',
        ]);

        $goods = new Size($this->space, $data['size']);
        $goods->setUser($this->user);
        $result = $goods->purchase();
        return redirect($result['pay_url']);
    }
}
