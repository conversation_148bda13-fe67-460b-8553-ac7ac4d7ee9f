<?php

namespace app\controller\webhook;

use app\BaseController;
use app\lib\Cloud;
use app\lib\Hashids;
use app\model\Order;

class ChargeController extends BaseController
{
    public function paid(Cloud $cloud)
    {
        $data = $cloud->verifyChargeNotify($this->request);
        $id   = Hashids::decode($data['order_no'], true, 'order');

        $order = Order::findOrFail($id);
        $order->paid();
    }

    public function revoked(Cloud $cloud)
    {
        $data = $cloud->verifyChargeNotify($this->request);
        $id   = Hashids::decode($data['order_no'], true, 'order');

        $order = Order::findOrFail($id);
        $order->revoked();
    }
}
