<?php

namespace app\job;

use app\event\BookReleaseFailed;
use app\event\BookReleaseSucceed;
use app\model\Book;
use app\model\User;
use Exception;
use RuntimeException;
use Swoole\Coroutine\System;
use think\Event;
use think\Filesystem;
use think\queue\Job;

class ReleaseBook
{
    public function __construct(
        protected Filesystem $filesystem,
        protected Event      $event
    )
    {
    }

    public function fire(Job $job, $payload)
    {
        [$user, $id, $sha] = $payload;

        $user        = User::findOrFail($user);
        $book        = Book::findOrFail($id);
        $releaseDisk = $this->filesystem->disk('release');
        $pagesDisk   = $this->filesystem->disk('pages');

        try {
            $name = "{$book->hash_path}/json/{$sha}.tar.gz";

            if (!$releaseDisk->has($name)) {
                $job->delete();
                return;
            }

            $filename = $releaseDisk->path($name);

            $root = "{$book->hash_path}/{$sha}";
            if ($pagesDisk->has($root)) {
                $pagesDisk->deleteDir($root);
            }

            $pagesDisk->createDir($root);

            //解压
            System::exec("tar -xzvf {$filename} -C {$pagesDisk->path($root)}");

            try {
                $pages = $book->getPages($sha);
            } catch (Exception) {
                throw new RuntimeException('文档解压失败，请重新发布');
            }

            $pages->sync();

            $this->event->trigger(new BookReleaseSucceed($user, $book));

            //清理
            queue(ClearPages::class, [$book->id, $sha], queue: 'book');

            //内容审核
            if ($book->space->moderation) {
                queue(ModerateBook::class, [$book->id, $sha], queue: 'book');
            }

            //训练数据
            if ($book->canAsk()) {
                queue(TrainBook::class, [$book->id, $sha], queue: 'train');
            }

            //刷新缓存
            $domain = $book->space->parked_domain;
            if ($domain && $domain->cache) {
                $domain->purge();
            }
        } catch (Exception $e) {
            $this->event->trigger(new BookReleaseFailed($user, $book, $e->getMessage()));
            trace($e->getMessage(), 'error');
            trace($e->getTraceAsString(), 'error');
        }

        $job->delete();
    }
}
