<?php

namespace app\lib;

use app\model\Order;
use app\model\User;
use yunwuxin\model\SerializesModel;

abstract class Goods
{
    use SerializesModel;

    /** @var User */
    protected $user;

    abstract public function getSubject();

    abstract public function getAmount();

    public function getReturnUrl(): ?string
    {
        return null;
    }

    /**
     * @param User $user
     * @return \app\lib\Goods
     */
    public function setUser($user)
    {
        $this->user = $user;
        return $this;
    }

    public function revoke(Order $order)
    {

    }

    abstract public function invoke(Order $order);

    public function canRevoke()
    {
        return false;
    }

    public function purchase()
    {
        $order = Order::create([
            'user_id' => $this->user?->id ?? 0,
            'subject' => $this->getSubject(),
            'amount'  => $this->getAmount(),
            'goods'   => $this,
        ]);

        return $order->pay();
    }
}
