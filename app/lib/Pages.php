<?php

namespace app\lib;

use app\lib\book\Config;
use app\lib\book\Summary;
use app\model\Book;
use app\model\Space;
use Exception;
use InvalidArgumentException;
use Qdrant\Models\Filter\Condition\MatchAny;
use Qdrant\Models\Filter\Condition\MatchString;
use Qdrant\Models\Filter\Filter;
use Qdrant\Models\PointsStruct;
use Qdrant\Models\Request\CreateCollection;
use Qdrant\Models\Request\CreateIndex;
use Qdrant\Models\Request\SearchRequest;
use Qdrant\Models\Request\VectorParams;
use Qdrant\Models\VectorStruct;
use Ramsey\Uuid\Uuid;
use Swoole\Coroutine;
use Swoole\Coroutine\WaitGroup;
use Symfony\Component\Filesystem\Path;
use Symfony\Component\Process\Process;
use think\ai\Client;
use think\exception\HttpException;
use think\facade\Cache;
use think\Filesystem;
use think\filesystem\Driver as Disk;
use think\helper\Arr;
use think\helper\Str;

class Pages
{
    protected $references = [];

    protected Space  $space;
    protected Disk   $disk;
    protected string $root;
    protected string $sha;

    protected $context;

    protected $config;

    /** @var Summary */
    protected $summary;

    public function __construct(protected Book $book, $sha = null)
    {
        $this->space   = $book->space;
        $this->sha     = $sha ?: $book->sha;
        $this->disk    = app(Filesystem::class)->disk('pages');
        $this->root    = Path::join($book->hash_path, $this->sha);
        $this->context = $this->readAsJson('context.json');

        if (empty($this->context)) {
            throw new InvalidArgumentException('pages not found');
        }
    }

    public function getConfig()
    {
        if (!$this->config) {
            if ($this->has('book.json')) {
                $content = $this->read('book.json');
            } else {
                $content = "";
            }

            $this->config = Config::fromString($content);
        }
        return $this->config;
    }

    public function getSummary()
    {
        if (!$this->summary) {
            if ($this->has('SUMMARY.json')) {
                $content = $this->read('SUMMARY.json');
            } else {
                $content = "";
            }

            $this->summary = Summary::createFromJson($content);
        }
        return $this->summary;
    }

    public function getContext()
    {
        return $this->context;
    }

    public function read($path)
    {
        $filename = $this->getFilename($path);
        return $this->disk->read($filename);
    }

    public function readAsJson($path)
    {
        $content = $this->read($path);
        if ($content) {
            return json_decode($content, true);
        }
        return null;
    }

    public function root()
    {
        return $this->disk->path($this->root);
    }

    public function path($path)
    {
        $filename = $this->getFilename($path);
        return $this->disk->path($filename);
    }

    public function has($path)
    {
        $filename = $this->getFilename($path);
        return $this->disk->has($filename);
    }

    protected function searchFiles($keyword, $articles)
    {
        $results = [];
        $files   = [];

        foreach ($articles as $article) {
            $result = [
                'title' => $article['title'],
                'path'  => $article['path'],
            ];
            if (mb_stripos($article['title'], $keyword) !== false) {
                $results[] = $result;
            } else {
                $files[$article['path']] = $result;
            }
        }

        try {
            $keyword = str_replace('"', '\"', $keyword);
            $process = Process::fromShellCommandline("rg \"{$keyword}\" . --no-ignore --type-add book:*.html.json --type book -l -i");

            $process->setWorkingDirectory($this->root());

            $process->mustRun();

            $list = explode("\n", trim($process->getOutput()));

            foreach ($list as $file) {
                $file = rtrim(ltrim($file, './'), '.json');

                if (array_key_exists($file, $files)) {
                    $results[] = $files[$file];
                }
            }
        } catch (Exception) {

        }
        return $results;
    }

    /**
     * @param $reference
     * @return Book
     */
    protected function getReference($reference)
    {
        if (!array_key_exists($reference, $this->references)) {
            $id = Hashids::decode($reference, false);
            if (empty($id)) {
                $this->references[$reference] = null;
            } else {
                $this->references[$reference] = $this->space->books()->find($id);
            }
        }

        return $this->references[$reference];
    }

    public function matchReference($path)
    {
        $parts     = explode('/', $path);
        $reference = array_shift($parts);

        $query = $this->space->books();
        try {
            $query->where('id', Hashids::decode($reference));
        } catch (HttpException) {
            $query->where('slug', $reference);
        }
        $book = $query->find();

        //TODO 完善完整路径检查
        return !!$book;
    }

    protected function searchReferencePages($keyword, $reference)
    {
        //关联文档
        $book = $this->getReference($reference);
        if ($book && $book->isReleased()) {
            $pages = $book->getPages();
            return array_map(function ($result) use ($book) {
                $slug = $book->slug ?: $book->hash_id;

                $result['path'] = "{$slug}/{$result['path']}";
                return $result;
            }, $pages->search($keyword, false));
        }
        return [];
    }

    public function search($keyword, $p = null)
    {
        $summary = $this->getSummary();

        if ($p) {
            $part = $summary->getPart($p);
            if ($part) {
                $reference = $part->getMetadata('reference');
                if ($reference) {
                    //关联文档
                    return $this->searchReferencePages($keyword, $reference);
                } else {
                    $articles = [];
                    $part->getArticle(function ($article) use (&$articles) {
                        $articles[] = $article;
                        return false;
                    });

                    return $this->searchFiles($keyword, $articles);
                }
            }
            return [];
        } else {
            $results = [];

            $wg = new WaitGroup();

            $articles = [];
            $summary->getArticle(function ($article) use (&$articles) {
                $articles[] = $article;
                return false;
            }, function ($part) use ($wg, $p, $keyword, &$results) {
                if ($p !== false) {
                    $reference = $part->getMetadata('reference');
                    if ($reference) {
                        //关联文档
                        $wg->add();
                        Coroutine::create(function () use ($reference, $keyword, $wg, &$results) {
                            $results[] = $this->searchReferencePages($keyword, $reference);
                            $wg->done();
                        });
                        return false;
                    }
                }
                return true;
            });

            $wg->add();
            Coroutine::create(function () use ($articles, $wg, $keyword, &$results) {
                $results[] = $this->searchFiles($keyword, $articles);
                $wg->done();
            });

            $wg->wait();

            return array_reduce($results, function ($carry, $item) {
                return array_merge($carry, $item);
            }, []);
        }
    }

    public function dataset()
    {
        $summary = $this->getSummary();

        $books = [$this->book->hash_id];

        foreach ($summary->getParts() as $part) {
            $reference = $part->getMetadata('reference');
            if ($reference) {
                $book = $this->getReference($reference);
                if ($book) {
                    $books[] = $book->hash_id;
                }
            }
        }

        return [
            'model'      => 'openai',
            'collection' => "space@{$this->space->hash_id}",
            'books'      => $books,
        ];
    }

    public function searchByVector($question, $p = null, $limit = 3, $score = 0.8)
    {
        $qdrant = app(Qdrant::class);
        $client = app(Client::class);

        if (is_string($question)) {
            $key = "page-search-" . md5($question) . '-vector';

            $vector = Cache::remember($key, function () use ($client, $question) {
                $result = $client->embeddings()->create([
                    'model' => 'openai',
                    'input' => $question,
                ]);

                $usage      = $result['usage']['total_tokens'];
                $embeddings = $result['embeddings'];

                $this->space->consumeAiTokens($usage);

                return $embeddings;
            }, 60);
        } else {
            $vector = $question;
        }

        $searchFilter = new Filter();

        $summary = $this->getSummary();

        if ($p) {
            $part = $summary->getPart($p);
            if ($part) {
                $reference = $part->getMetadata('reference');
                if ($reference) {
                    $book = $this->getReference($reference);
                    if ($book) {
                        $searchFilter->addMust(new MatchString('book', $book->hash_id));
                    }
                } else {
                    $refs = [];
                    $part->getArticle(function ($article) use (&$refs) {
                        $refs[] = $article['ref'];
                        return false;
                    });

                    $searchFilter->addMust(new MatchString('book', $this->book->hash_id));
                    $searchFilter->addMust(new MatchAny('ref', $refs));
                }
            }
        } else {
            $books = [$this->book->hash_id];
            foreach ($summary->getParts() as $part) {
                $reference = $part->getMetadata('reference');
                if ($reference) {
                    $book = $this->getReference($reference);
                    if ($book) {
                        $books[] = $book->hash_id;
                    }
                }
            }

            $searchFilter->addMust(new MatchAny('book', $books));
        }

        $searchRequest = (new SearchRequest(new VectorStruct($vector, '')))
            ->setFilter($searchFilter)
            ->setLimit($limit)
            ->setWithPayload(true);

        if (!is_null($score)) {
            $searchRequest->setScoreThreshold($score);
        }

        try {
            $response = $qdrant->collections("space@{$this->space->hash_id}")->points()->search($searchRequest);

            $results = $response['result'];

            if (!empty($results)) {
                //TODO 处理本地图片
                $content = array_reduce($results, function ($carry, $item) {
                    $content = $item['payload']['content'];
                    return "{$carry}{$content}\n";
                }, '');

                $refs = array_unique(array_map(fn($chunk) => "{$chunk['payload']['ref']}@{$chunk['payload']['book']}", $results));

                $uri    = $this->book->uri;
                $domain = $this->space->domain;
                if ($this->space->isOrg()) {
                    if (
                        $this->space->owner->index instanceof Book
                        && $this->space->owner->index->id == $this->book->id
                    ) {
                        //是否为空间的默认主页
                        $uri = "/";
                    }
                    if ($this->space->parked_domain) {
                        //自动使用绑定域名
                        $domain = $this->space->parked_domain->name;
                    }
                }

                $base = url($uri)->domain($domain);

                $relevant = array_flat_map(function ($ref) use ($base) {
                    [$ref, $reference] = explode('@', $ref);

                    $book = $this->book;
                    $dir  = '';
                    if ($reference != $this->book->hash_id) {
                        $book = $this->getReference($reference);
                        if ($book) {
                            $dir = $book->slug ?: $book->hash_id;
                        }
                    }

                    if ($book) {
                        $article = $book->articles()->where('ref', $ref)->find();
                        if ($article) {
                            return [
                                [
                                    'title' => $article->title,
                                    'ref'   => Path::join($dir, $ref),
                                    'url'   => Path::join($base, $dir, $article->path),
                                ],
                            ];
                        }
                    }
                    return [];
                }, $refs);

                return [
                    'content'  => $content,
                    'relevant' => $relevant,
                ];
            } else {
                return [
                    'error' => 'Not Found',
                ];
            }
        } catch (Exception) {
            throw new HttpException('400', '文档未开启智问搜索或尚未训练完成');
        }
    }

    /**
     * @return \Generator
     */
    public function ask($question, $p = null)
    {
        $client = app(Client::class);

        $result = $this->searchByVector($question, $p);

        $toolId = uniqid();

        $messages = [
            [
                'role'    => 'system',
                'content' => "
## 角色
你是一个智能助手,可以根据知识库的内容回答用户的问题。

## 技能
- 你可以使用知识库中的知识，调用知识库搜索相关知识，并向用户提供简洁和专业的答案。
- 如果知识库中没有相关知识，你可以向用户表明该问题超出了知识库的范围。

## 限制
- 你的回答必须基于知识库中的内容，不能包含编造成分。
- 你的回答必须简洁明了，不能过于复杂。
- 你的回答必须专业，不能使用口语化的表达方式。
- 避免提及你是从知识库获取的知识。
- 使用 Markdown 语法优化回答格式。
- 使用与问题相同的语言回答。
",
            ],
            [
                'role'    => 'user',
                'content' => $question,
            ],
            [
                'role'       => 'assistant',
                'tool_calls' => [
                    [
                        'id'       => $toolId,
                        'type'     => 'function',
                        'function' => [
                            'name'      => 'dataset-search',
                            'arguments' => json_encode(['query' => $question]),
                        ],
                    ],
                ],
            ],
            [
                'role'         => 'tool',
                'tool_call_id' => $toolId,
                'content'      => $result['content'] ?? '[未找到相关知识]',
            ],
        ];

        $relevant = !empty($result['relevant']) ? array_reduce($result['relevant'], function ($carry, $item) {
            return "{$carry}\n > * []({$item['ref']})";
        }, "> **相关章节**") : '';

        $result = $client->chat()->completions([
            'model'    => 'gpt-3.5-turbo',
            'messages' => $messages,
        ]);

        foreach ($result as $data) {
            $text  = Arr::get($data, 'delta.content');
            $usage = Arr::get($data, 'usage');

            if (!is_null($text)) {
                yield $text;
            }
            if ($usage) {
                $this->space->consumeAiTokens($usage['total_tokens']);
            }
        }

        yield "\n\n{$relevant}";
    }

    public function sync()
    {
        $this->book->transaction(function () {
            //更新文档信息
            $articles = $this->book->articles()->select();
            $refs     = [];
            $this->getSummary()->getArticle(function ($article) use (&$articles, &$refs) {
                $ctime = !empty($article['metadata']['ctime']) ? Date::parse($article['metadata']['ctime']) : null;
                $mtime = !empty($article['metadata']['mtime']) ? Date::parse($article['metadata']['mtime']) : null;
                $trial = $article['metadata']['trial'] ?? false;

                if (!empty($ctime) && !in_array($article['ref'], $refs)) {
                    $refs[] = $article['ref'];
                    /** @var \app\model\BookArticle $exist */
                    $exist = $articles->where('ref', $article['ref'])->first();

                    if ($exist) {
                        $exist->save([
                            'title'       => $article['title'],
                            'path'        => $article['path'],
                            'create_time' => $ctime,
                            'update_time' => $mtime,
                            'trial'       => $trial,
                        ]);
                        $articles = $articles->where('id', '!=', $exist->id);
                    } else {
                        $this->book->articles()->save([
                            'title'       => $article['title'],
                            'path'        => $article['path'],
                            'ref'         => $article['ref'],
                            'create_time' => $ctime,
                            'update_time' => $mtime,
                            'trial'       => $trial,
                        ]);
                    }
                }
            });

            //删除多余的文档
            $articles->delete();

            //更新标题
            $config = $this->getConfig();
            $title  = $config->getValue('title', $this->book->name);

            $this->book->isAutoWriteTimestamp(false)->save([
                'name'         => $title,
                'sha'          => $this->sha,
                'release_time' => Date::now(),
            ]);
        });
    }

    public function train()
    {
        $qdrant    = app(Qdrant::class);
        $client    = app(Client::class);
        $separator = app(Separator::class);

        $collection = $qdrant->collections("space@{$this->space->hash_id}");

        try {
            //创建集合
            $createCollection = new CreateCollection();
            $createCollection->addVector(new VectorParams(1536, VectorParams::DISTANCE_COSINE));
            $collection->create($createCollection);

            //创建索引
            $index = $collection->index();
            $index->create(new CreateIndex('book', 'keyword'));
            $index->create(new CreateIndex('ref', 'keyword'));
        } catch (\Qdrant\Exception\InvalidArgumentException $e) {
            if (!Str::contains($e->getMessage(), 'already exists')) {
                throw $e;
            }
        }

        $points   = $collection->points();
        $articles = $this->book->articles()->select()->column(null, 'ref');

        $balance = $this->space->ai_tokens;

        /** @var \app\model\BookArticle $article */
        foreach ($articles as $article) {
            if ($balance <= 0) {
                //Tokens不足，退出
                break;
            }

            if (!$article->update_time || ($article->train_time && $article->train_time->eq($article->update_time))) {
                //无需更新
                continue;
            }

            //删除旧数据
            $filter = new Filter();
            $filter->addMust(new MatchString('book', $this->book->hash_id));
            $filter->addMust(new MatchString('ref', $article->ref));
            $points->deleteByFilter($filter);

            //插入新数据
            $file = $this->readAsJson($article->path . '.json');
            if ($file) {
                $chunks = $separator->separate("# {$article->title}\n\n{$file['content']}");

                if (!empty($chunks)) {
                    foreach (array_chunk($chunks, 10) as $items) {
                        $result = $client->embeddings()->create([
                            'model' => 'openai',
                            'input' => $items,
                        ]);

                        $usage      = $result['usage']['total_tokens'];
                        $embeddings = $result['embeddings'];

                        //计费
                        $this->space->consumeAiTokens($result['usage']['total_tokens']);
                        $balance -= $usage;

                        $points->upsert(PointsStruct::createFromArray(array_map(function ($item, $key) use ($article, $embeddings) {
                            return [
                                'id'      => Uuid::uuid1()->toString(),
                                'vector'  => $embeddings[$key],
                                'payload' => [
                                    'book'    => $this->book->hash_id,
                                    'ref'     => $article->ref,
                                    'content' => $item,
                                ],
                            ];
                        }, $items, array_keys($items))));
                    }
                }
            }

            $article->save(['train_time' => $article->update_time]);
        }

        //清理不存在的章节
        $filter = new Filter();
        $filter->addMust(new MatchString('book', $this->book->hash_id));
        $filter->addMustNot(new MatchAny('ref', array_keys($articles)));
        $points->deleteByFilter($filter);
    }

    public function moderate()
    {
        $moderation = new Moderation();
        $articles   = $this->book->articles()->select()->column(null, 'ref');

        /** @var \app\model\BookArticle $article */
        foreach ($articles as $article) {
            if (!$article->update_time || ($article->moderate_time && $article->moderate_time->eq($article->update_time))) {
                //无需更新
                continue;
            }

            $file = $this->readAsJson($article->path . '.json');

            $status = 1;

            if ($file) {
                $remark = $moderation->text($file['content']);
                if (!empty($remark) && !($remark == $article->remark && $article->status == 1)) {
                    $status = 0;
                }
            }

            $article->save([
                'remark'        => $remark ?? null,
                'status'        => $status,
                'moderate_time' => $article->update_time,
            ]);
        }
    }

    protected function getFilename($path)
    {
        return Path::join($this->root, $path);
    }
}
