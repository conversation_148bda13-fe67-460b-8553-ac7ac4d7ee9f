<?php

namespace app\lib;

use yunwuxin\auth\provider\Model;

class UserProvider extends Model
{
    /**
     * @param $user
     * @return mixed|void
     */
    public function getId($user)
    {
        if ($user instanceof SamlUser) {
            return json_encode($user->toArray());
        }
        return parent::getId($user);
    }

    public function retrieveById($id)
    {
        if (is_numeric($id)) {
            return parent::retrieveById($id);
        }
        $data = json_decode($id, true);
        if (is_array($data)) {
            return new SamlUser($data);
        }
        return null;
    }

}
