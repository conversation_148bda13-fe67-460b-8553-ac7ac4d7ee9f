<?php

namespace app\lib\goods;

use app\lib\Goods;
use app\model\Order;
use app\model\OrganizationEarnings;

class Book extends Goods
{
    public function __construct(public \app\model\Book $book, public $returnUrl)
    {
    }

    public function getSubject()
    {
        return "文档〈{$this->book->name}〉";
    }

    public function getAmount()
    {
        return $this->book->price * 100;
    }

    public function getReturnUrl(): ?string
    {
        return $this->returnUrl;
    }

    public function canRevoke()
    {
        return true;
    }

    public function revoke(Order $order)
    {
        $order->payable->earnings?->delete();
        $order->payable->delete();
    }

    public function invoke(Order $order)
    {
        $amount = $this->getAmount();

        $sale = $this->book->sales()->save([
            'user_id' => $this->user->id,
            'amount'  => $amount,
        ]);

        //分成
        if ($amount > 0) {
            //分成比例
            $rate = $this->book->space->isEnterprisePlan() ? 0.8 : 0.7;

            $this->book->space->owner->updateEarnings(
                OrganizationEarnings::TYPE_INC,
                round($amount * $rate),
                "文档[{$this->book->name}]销售收入",
                $sale
            );
        }
        return $sale;
    }
}
