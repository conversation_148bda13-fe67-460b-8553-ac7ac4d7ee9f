<?php

namespace app\lib\goods;

use app\lib\Date;
use app\lib\Goods;
use app\model\Order;
use app\model\Space;

class Plan extends Goods
{
    const TEAM       = 'team';
    const ENTERPRISE = 'enterprise';

    const PRICES = [
        self::ENTERPRISE => 1888,
        self::TEAM       => 288,
    ];

    public function __construct(public Space $space, public $name, public $years)
    {

    }

    public function getSubject()
    {
        $title = [
            self::ENTERPRISE => '企业版',
            self::TEAM       => '团队版',
        ];
        return "{$title[$this->name]}{$this->years}年";
    }

    public function getAmount()
    {
        return (self::PRICES[$this->name] + $this->space->size * Size::PRICE + $this->space->tokens * Tokens::PRICE) * $this->years * 100;
    }

    public function getReturnUrl(): ?string
    {
        return (string) url('/-/org/billing')->domain(true);
    }

    public function invoke(Order $order)
    {
        $expire_time = $this->space->expire_time;
        if (is_null($expire_time) || $expire_time->lt(Date::now())) {
            $expire_time = Date::now();
        }

        $this->space->expire_time = $expire_time->add("{$this->years} year");
        $this->space->plan        = $this->name;
        $this->space->save();
    }
}
