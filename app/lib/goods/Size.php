<?php

namespace app\lib\goods;

use app\lib\Date;
use app\lib\Goods;
use app\model\Order;
use app\model\Space;

class Size extends Goods
{
    const PRICE = 100;//单价 1G/年

    public function __construct(public Space $space, public $size)
    {

    }

    public function getSubject()
    {
        return "空间扩容{$this->size}GiB";
    }

    public function getAmount()
    {
        $days = $this->space->expire_time->diffInDays(Date::now());
        return floor($this->size * self::PRICE * $days / 365) * 100;
    }

    public function getReturnUrl(): ?string
    {
        return (string) url('/-/org/billing')->domain(true);
    }

    public function invoke(Order $order)
    {
        $this->space->size += $this->size;
        $this->space->save();
    }
}
