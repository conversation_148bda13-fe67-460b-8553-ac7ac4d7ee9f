<?php

namespace app\lib\goods;

use app\lib\Date;
use app\lib\Goods;
use app\model\Order;
use app\model\Space;

class Tokens extends Goods
{
    const PRICE = 0.24; //单价 K/年

    public function __construct(public Space $space, public $tokens)
    {

    }

    public function getSubject()
    {
        return "Tokens额度提升{$this->tokens}K";
    }

    public function getAmount()
    {
        $days = $this->space->expire_time->diffInDays(Date::now());
        return floor($this->tokens * self::PRICE * $days / 365) * 100;
    }

    public function getReturnUrl(): ?string
    {
        return (string) url('/-/org/billing')->domain(true);
    }

    public function invoke(Order $order)
    {
        $this->space->tokens += $this->tokens;
        $this->space->save();
    }
}
