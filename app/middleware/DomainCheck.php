<?php

namespace app\middleware;

use app\lib\Hashids;
use app\model\Domain;
use app\model\Space;
use app\Request;
use Closure;
use think\Config;
use think\exception\HttpException;
use think\helper\Str;
use think\Response;
use yunwuxin\Auth;

class DomainCheck
{
    public function __construct(protected Config $config, protected Auth $auth)
    {
    }

    /**
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle($request, Closure $next)
    {
        if (is_saas()) {
            $host    = $request->host();
            $appHost = config('app.host');

            if ($host != $appHost) {
                $rootDomain = '.' . $appHost;
                if (Str::endsWith($host, $rootDomain)) {
                    $subDomain = stristr($host, $rootDomain, true);
                } else {
                    $subDomain = $host;
                }

                if (is_valid_domain($subDomain)) {
                    $domain = Domain::where('name', $subDomain)->findOrFail();

                    //设置通过绑定域名访问时的cookie域
                    $this->config->set(['domain' => ".{$domain->name}"], 'cookie');

                    $space = $domain->space;

                    if (empty($space)) {
                        //空间被删除
                        throw new HttpException(404);
                    }

                    $request->setMode(Request::DOMAIN_PARKED);
                    $request->setRealDomain($domain);
                } else {
                    $space = Space::findOrFail(Hashids::decode($subDomain));
                    $request->setMode(Request::DOMAIN_SUB);
                }

                $request->setSpace($space);
            }
        } else {
            $request->setSpace(get_default_space());
        }
        return $next($request);
    }
}
