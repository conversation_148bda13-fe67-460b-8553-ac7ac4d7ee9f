<?php

namespace app\model;

use think\Model;
use think\View;

/**
 * Class app\model\Activity
 *
 * @property \app\lib\Date $create_time
 * @property mixed $book_id
 * @property mixed $content
 * @property mixed $id
 * @property mixed $owner_id
 * @property mixed $owner_type
 * @property mixed $space_id
 * @property mixed $type
 * @property mixed $user_id
 * @property-read \app\model\Space $space
 * @property-read \app\model\User $user
 * @property-read mixed $book
 * @property-read mixed $html
 * @property-read mixed $owner
 */
class Activity extends Model
{
    const TYPE_BOOK_UPDATE             = 1;
    const TYPE_BOOK_RELEASE            = 2;
    const TYPE_BOOK_CREATE             = 3;
    const TYPE_BOOK_DELETE             = 4;
    const TYPE_BOOK_RESTORE            = 5;
    const TYPE_BOOK_RELEASE_FAIL       = 6;
    const TYPE_BOOK_ARTICLE_CHECK_FAIL = 7;

    protected $append = ['html'];

    protected $updateTime = false;

    public function owner()
    {
        return $this->morphTo();
    }

    public function user()
    {
        return $this->belongsTo(User::class)->withDefault(new User(['name' => '知识管理', 'avatar' => null]));
    }

    public function book()
    {
        return $this->belongsTo(Book::class)->withTrashed();
    }

    public function space()
    {
        return $this->belongsTo(Space::class);
    }

    protected function getHtmlAttr()
    {
        return $this->invoke(function (View $view) {
            try {
                return match ((int) $this->getAttr('type')) {
                    self::TYPE_BOOK_UPDATE => $view->fetch('activity/book', ['activity' => $this, 'message' => '更新了文档']),
                    self::TYPE_BOOK_RELEASE => $view->fetch('activity/release', ['activity' => $this]),
                    self::TYPE_BOOK_CREATE => $view->fetch('activity/book', ['activity' => $this, 'message' => '创建了文档']),
                    self::TYPE_BOOK_DELETE => $view->fetch('activity/book', ['activity' => $this, 'message' => '删除了文档']),
                    self::TYPE_BOOK_RESTORE => $view->fetch('activity/book', ['activity' => $this, 'message' => '恢复了文档']),
                    self::TYPE_BOOK_RELEASE_FAIL => $view->fetch('activity/release_fail', ['activity' => $this, 'error' => $this->content]),
                    self::TYPE_BOOK_ARTICLE_CHECK_FAIL => $view->fetch('activity/article_check_fail', ['activity' => $this]),
                    default => $this->content,
                };
            } catch (\Throwable) {
                return $view->fetch('activity/exception');
            }
        });
    }
}
