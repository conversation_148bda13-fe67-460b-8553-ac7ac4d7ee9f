<?php

namespace app\model;

use app\lib\Cloud;
use app\lib\Date;
use app\lib\Hashids;
use app\lib\Pages;
use app\lib\StaticData;
use Exception;
use Symfony\Component\Filesystem\Path;
use think\Cache;
use think\db\Query;
use think\Filesystem;
use think\Model;
use think\model\concern\SoftDelete;
use topthink\git\Repository;

/**
 * Class app\model\Book
 *
 * @property \app\lib\Date $block_time
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $release_time
 * @property \app\lib\Date $update_time
 * @property array $pack_types
 * @property bool $ask
 * @property bool $pay_read
 * @property bool $token_read
 * @property bool $write_release
 * @property int $price
 * @property mixed $delete_time
 * @property mixed $id
 * @property mixed $is_template
 * @property mixed $name
 * @property mixed $original_path
 * @property mixed $original_type
 * @property mixed $space_id
 * @property mixed $status
 * @property mixed $visibility_level
 * @property string $sha
 * @property string $slug
 * @property-read \app\model\AccessToken $access_tokens
 * @property-read \app\model\BookArticle[] $articles
 * @property-read \app\model\BookMember[] $members
 * @property-read \app\model\BookSale[] $sales
 * @property-read \app\model\LfsObject[] $lfs_objects
 * @property-read \app\model\Space $space
 * @property-read array $masters
 * @property-read mixed $available_pack_types
 * @property-read mixed $git_url
 * @property-read mixed $hash_id
 * @property-read mixed $hash_path
 * @property-read mixed $lfs_url
 * @property-read mixed $logo
 * @property-read mixed $repo_path
 * @property-read mixed $ssh_url
 * @property-read mixed $uri
 * @property-read mixed $url
 * @property-read mixed $web_url
 * @property-read bool $is_sale
 * @property-read string|false $weapp
 * @method static \think\db\Query onlyTrashed()
 * @method static \think\db\Query spaceExist()
 * @method static \think\db\Query userBooks(\app\model\Space $space, \app\model\User $user)
 * @method static \think\db\Query withTrashed()
 */
class Book extends Model
{
    use SoftDelete, StaticData;

    const LEVEL_PUBLIC    = 20;
    const LEVEL_PROTECTED = 10;
    const LEVEL_PRIVATE   = 0;

    const PACK_TYPES = [
        'pdf'  => '.pdf',
        'epub' => '.epub',
        'word' => '.docx',
        'html' => '.tar.gz',
        'json' => '.tar.gz',
    ];

    protected $type = [
        'delete_time'  => Date::class,
        'release_time' => Date::class,
        'block_time'   => Date::class,
        'pay_read'     => 'boolean',
    ];

    protected $hidden = ['original_type', 'original_path'];

    protected $append = ['logo', 'is_sale'];

    public static function onAfterRead(self $model): void
    {
        if (
            ($model->status == 0 || $model->status > 1) && $model->update_time->lt(Date::now()->sub('10 minute'))
        ) {
            $model->save([
                'status' => -1,
            ]);
        }
    }

    public function accessTokens()
    {
        return $this->morphMany(AccessToken::class, 'accessible');
    }

    public function members()
    {
        return $this->hasMany(BookMember::class);
    }

    public function articles()
    {
        return $this->hasMany(BookArticle::class);
    }

    public function lfsObjects()
    {
        return $this->belongsToMany(LfsObject::class, 'lfs_object_book');
    }

    public function space()
    {
        return $this->belongsTo(Space::class);
    }

    /**
     * 关联销售记录
     */
    public function sales()
    {
        return $this->hasMany(BookSale::class);
    }

    public function canAsk()
    {
        if ($this->space->ai_tokens <= 0) {
            return false;
        }

        return !!$this->ask;
    }

    public function isBlocked()
    {
        return !!$this->block_time;
    }

    public function isPublic()
    {
        return $this->visibility_level == self::LEVEL_PUBLIC;
    }

    protected function getIsSaleAttr()
    {
        return $this->isPublic() && $this->pay_read;
    }

    /**
     * 获取价格（元）
     */
    protected function getPriceAttr($value)
    {
        return format_price($value);
    }

    /**
     * 设置价格（元转分）
     */
    protected function setPriceAttr($value)
    {
        return (float) $value * 100;
    }

    protected function getWriteReleaseAttr($value)
    {
        if (!$this->space->isEnterprisePlan()) {
            return true;
        }
        return !!$value;
    }

    protected function getUrlAttr()
    {
        return (string) url("/-/book/{$this->hash_id}/dashboard")->domain($this->space->domain);
    }

    protected function getUriAttr()
    {
        $slug = $this->slug ?: $this->hash_id;
        return "/@{$slug}";
    }

    protected function getWebUrlAttr()
    {
        return (string) url($this->uri)->domain($this->space->domain);
    }

    protected function getGitUrlAttr()
    {
        return (string) url("/{$this->hash_id}.git")->domain($this->space->domain);
    }

    protected function getSshUrlAttr()
    {
        $port = config('app.ssh_port');
        if (empty($port)) {
            return null;
        }
        $host = config('app.host');

        $url = "git@{$host}";

        if ($port != 22) {
            $url = "ssh://{$url}:{$port}/";
        } else {
            $url = "{$url}:";
        }

        if (is_saas()) {
            $path = "{$this->space->hash_id}/{$this->hash_id}.git";
        } else {
            $path = "{$this->hash_id}.git";
        }

        return "{$url}{$path}";
    }

    protected function getLfsUrlAttr()
    {
        return (string) url('/lfs/{oid}', domain: true);
    }

    protected function getLogoAttr()
    {
        if ($this->isReleased()) {
            try {
                $content = $this->getPages()->read('logo.png');
                return sprintf(
                    'data:%s;base64,%s',
                    'image/png',
                    base64_encode($content)
                );
            } catch (Exception) {
                //logo获取失败时 返回默认logo
            }
        }
        return $this->space->logo;
    }

    /**
     * 管理员
     */
    protected function getMastersAttr()
    {
        return $this->members()->where('access_level', Member::MASTER)->select();
    }

    /**
     * @param \app\model\User $user
     */
    public function hasBuyer($user)
    {
        return !!$this->sales()->where('user_id', $user->id)->find();
    }

    /**
     * @param User $member
     * @return bool
     */
    public function hasMember($member, $accessLevel = null, $type = null)
    {
        /** @var BookMember $pivot */
        $pivot = $this->getStaticData("member-{$member->id}", function () use ($member) {
            return $this->members()->where(function (Query $query) use ($member) {
                $query->whereOr(function (Query $query) use ($member) {
                    $query->where('member_type', User::class);
                    $query->where('member_id', $member->id);
                });
                $query->whereOr(function (Query $query) use ($member) {
                    $query->where('member_type', Team::class);
                    $query->whereIn('member_id', function (Query $query) use ($member) {
                        $query->table('team_member')
                            ->where('user_id', $member->id)
                            ->field('team_id');
                    });
                });
            })->order('create_time desc')->find();
        });

        return $pivot && ($accessLevel == null || $pivot->access_level >= $accessLevel) && ($type == null || $pivot->member_type == $type);
    }

    /**
     * @param User|Team $member
     */
    public function addMember($member, $accessLevel = Member::READER, $overwrite = true)
    {
        /** @var BookMember $pivot */
        $pivot = $this->members()->where([
            'member_id'   => $member->id,
            'member_type' => get_class($member),
        ])->find();

        if ($overwrite || !$pivot) {
            $this->members()->save([
                'member_id'    => $member->id,
                'member_type'  => get_class($member),
                'access_level' => $accessLevel,
                'create_time'  => $pivot?->create_time,
            ]);
        }
    }

    public function isReleased()
    {
        return !empty($this->sha);
    }

    public function getPages($sha = null)
    {
        return new Pages($this, $sha);
    }

    public function repo()
    {
        return new Repository($this->repo_path);
    }

    protected function getHashIdAttr()
    {
        return Hashids::encode($this->id);
    }

    protected function getHashPathAttr()
    {
        $hash = sha1($this->id);
        return substr($hash, 0, 2) . '/' . substr($hash, 2, 2) . "/{$hash}";
    }

    protected function getRepoPathAttr()
    {
        return $this->invoke(function (Filesystem $filesystem) {
            return $filesystem->disk('repositories')->path($this->hash_path);
        });
    }

    protected function getPackTypesAttr($value)
    {
        if (empty($value)) {
            return [];
        }
        return explode(',', $value);
    }

    protected function setPackTypesAttr($value)
    {
        return join(',', $value);
    }

    protected function getAvailablePackTypesAttr()
    {
        if (!$this->sha) {
            return [];
        }
        return $this->invoke(function (Filesystem $filesystem) {
            $disk = $filesystem->disk('release');
            return array_filter(array_keys(self::PACK_TYPES), function ($type) use ($disk) {
                return $disk->has(Path::join($this->hash_path, $type, $this->sha . self::PACK_TYPES[$type]));
            });
        });
    }

    /**
     * 获取小程序二维码
     * @return string|false
     */
    protected function getWeappAttr()
    {
        return $this->invoke(function (Cache $cache) {
            if ($this->space->isOrg() && $this->space->owner->weapp_id && $this->visibility_level >= Book::LEVEL_PUBLIC) {
                $weapp = $cache->remember("book-qrcode-{$this->hash_id}", function (Cloud $cloud) {
                    $id = $this->space->owner->weapp_id;
                    try {
                        $weapp = $cloud->getWeapp($id);

                        if ($weapp['status'] == 1 && $weapp['version']) {
                            $qrcode = $cloud->getWeappQrcode($id, 'pages/read/index', $this->hash_id);

                            return $qrcode['url'];
                        }
                    } catch (Exception) {

                    }

                    return false;
                }, 3600);
            } else {
                $weapp = false;
            }

            return $weapp;
        });
    }

    /**
     * 用户授权的文档
     * @param Query $query
     * @param Space $space
     * @param User $user
     * @return void
     */
    static public function scopeUserBooks(Query $query, Space $space, User $user, $scope = 'all')
    {
        if ($space->isOrg() && (!$user->can('admin', $space) || $scope != 'all')) {
            //非组织管理员时，需要检查文档权限
            $query->where(function (Query $query) use ($scope, $space, $user) {
                $query->whereIn('id', function (Query $query) use ($scope, $user, $space) {
                    $query
                        ->table('book_member')
                        ->where(function (Query $query) use ($scope, $space, $user) {
                            $query
                                ->whereOr(function (Query $query) use ($scope, $user) {
                                    $query
                                        ->where('member_id', $user->id)
                                        ->where('member_type', User::class);

                                    if ($scope == 'managed') {
                                        $query->where('access_level', Member::MASTER);
                                    }
                                });

                            if ($scope != 'managed') {
                                $query
                                    ->whereOr(function (Query $query) use ($space, $user) {
                                        $query
                                            ->whereIn('member_id', function (Query $query) use ($user, $space) {
                                                $query
                                                    ->table('team_member')
                                                    ->join('team', 'team.id=team_member.team_id')
                                                    ->where('team.org_id', $space->owner->id)
                                                    ->where('team_member.user_id', $user->id)
                                                    ->field('team_id');
                                            })
                                            ->where('member_type', Team::class);
                                    });
                            }
                        })
                        ->field('book_id');
                }, 'OR');

                if ($scope == 'all') {
                    $query->whereOr('visibility_level', '>=', self::LEVEL_PROTECTED);
                }
            });
        }
    }

    /**
     * 检查空间是否已删除
     * @param Query $query
     * @return void
     */
    public function scopeSpaceExist(Query $query)
    {
        $query->whereExists(function (Query $query) {
            $query->table('space')
                ->whereRaw('book.space_id=space.id')
                ->where(function (Query $query) {
                    $query->whereNull('space.delete_time', 'OR');
                    $query->whereOr('space.delete_time', '>', Date::now()->subDay());
                });
        });
    }
}
