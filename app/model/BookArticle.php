<?php

namespace app\model;

use app\lib\Date;
use think\db\Query;
use think\Model;

/**
 * Class app\model\BookArticle
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $moderate_time
 * @property \app\lib\Date $train_time
 * @property \app\lib\Date $update_time
 * @property bool $trial
 * @property int $book_id
 * @property int $id
 * @property int $status
 * @property string $path
 * @property string $ref
 * @property string $remark
 * @property string $title
 * @property-read \app\model\Book $book
 * @method static \think\db\Query bookExist()
 */
class BookArticle extends Model
{
    protected $autoWriteTimestamp = false;

    protected $type = [
        'train_time'    => Date::class,
        'moderate_time' => Date::class,
        'create_time'   => Date::class,
        'update_time'   => Date::class,
        'trial'         => 'boolean',
    ];

    public function book()
    {
        return $this->belongsTo(Book::class);
    }

    /**
     * 检查文档或空间是否已删除
     * @param Query $query
     * @return void
     */
    public function scopeBookExist(Query $query)
    {
        $query->whereExists(function (Query $query) {
            $query->table('book')
                ->whereRaw('book_article.book_id=book.id')
                ->where(function (Query $query) {
                    $query->whereNull('book.block_time')
                        ->whereNull('book.delete_time')
                        ->whereExists(function (Query $query) {
                            $query->table('space')
                                ->whereRaw('book.space_id=space.id')
                                ->where(function (Query $query) {
                                    $query->whereNull('space.delete_time', 'OR');
                                    $query->whereOr('space.delete_time', '>', Date::now()->subDay());
                                });
                        });
                });
        });
    }
}
