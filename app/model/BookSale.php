<?php

namespace app\model;

use app\lib\Date;
use think\Model;

/**
 * Class app\model\BookSales
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $amount 支付金额(分)
 * @property int $book_id 文档ID
 * @property int $id
 * @property int $user_id 购买用户ID
 * @property-read \app\model\Book $book
 * @property-read \app\model\Order $ord
 * @property-read \app\model\OrganizationEarnings $earnings
 * @property-read \app\model\User $user
 */
class BookSale extends Model
{

    /**
     * 关联书籍
     */
    public function book()
    {
        return $this->belongsTo(Book::class);
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function ord()
    {
        return $this->morphOne(Order::class, 'payable');
    }

    public function earnings()
    {
        return $this->morphOne(OrganizationEarnings::class, 'source');
    }

    public function canRevoke()
    {
        //7天内的可撤销
        return $this->create_time->gt(Date::now()->subDays(7));
    }

}
