<?php

namespace app\model;

use app\lib\Cdn;
use think\Model;

/**
 * Class app\model\Domain
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property bool $cdn
 * @property bool $cache
 * @property int $cert_id
 * @property int $id
 * @property int $space_id
 * @property int $status
 * @property string $dns_id
 * @property string $name
 * @property-read \app\model\Space $space
 * @property-read mixed $url
 */
class Domain extends Model
{
    public function space()
    {
        return $this->belongsTo(Space::class);
    }

    /**
     * 检查域名的CNAME值
     * @return Domain
     */
    public function verify()
    {
        $target = null;

        $cname = $this->space->cname;

        $records = dns_get_record($this->getAttr('name'), DNS_CNAME);

        foreach ($records as $record) {
            if ('CNAME' == $record['type']) {
                $target = $record['target'];
                if ($target == $cname) {
                    return $this;
                }
            }
        }

        throw new \InvalidArgumentException('域名尚未验证成功，CNAME 生效可能需要几分钟到几小时。当前 CNAME 值为：' . $target);
    }

    public function purge()
    {
        return $this->invoke(function (Cdn $cdn) {
            $cdn->purge($this);
        });
    }

    public function getUrlAttr()
    {
        $schema = $this->cert_id ? 'https' : 'http';
        return "{$schema}://{$this->getAttr('name')}";
    }

}
