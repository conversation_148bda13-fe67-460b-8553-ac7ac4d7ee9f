<?php

namespace app\model;

use app\event\BookCreated;
use app\job\InitBook;
use app\lib\Date;
use app\lib\Hashids;
use app\lib\Moderation;
use app\lib\plan\EnterprisePlan;
use app\lib\plan\TeamPlan;
use app\lib\plan\TrialPlan;
use app\lib\plan\UserPlan;
use Exception;
use think\Cache;
use think\db\Query;
use think\Model;
use think\model\Collection;
use think\saml\Auth;
use think\saml\ServiceProvider;

/**
 * Class app\model\Space
 *
 * @property \app\lib\Date $block_time
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $delete_time
 * @property \app\lib\Date $expire_time
 * @property \app\lib\Date $update_time
 * @property bool $preemptive
 * @property int $bytes
 * @property int $moderation
 * @property int $size
 * @property int $tokens Token额度
 * @property array $features 功能设置
 * @property mixed $id
 * @property mixed $owner_id
 * @property mixed $owner_type
 * @property mixed $plan
 * @property-read \ByteUnits\Metric $media_max_size
 * @property-read \ByteUnits\Metric $media_size
 * @property-read \app\lib\plan\BasePlan $current_plan
 * @property-read \app\model\Activity[] $activities
 * @property-read \app\model\Book[]|\think\model\Collection $books
 * @property-read \app\model\Domain $parked_domain
 * @property-read \app\model\User|\app\model\Organization $owner
 * @property-read mixed $access_tokens
 * @property-read mixed $ai_tokens
 * @property-read mixed $cdn_max_size
 * @property-read mixed $cdn_size
 * @property-read mixed $cname
 * @property-read mixed $domain
 * @property-read mixed $expire_message
 * @property-read mixed $hash_id
 * @property-read mixed $logo
 * @property-read mixed $max_ai_tokens
 * @property-read mixed $max_size
 * @property-read mixed $members_max_num
 * @property-read mixed $members_num
 * @property-read mixed $name
 * @property-read mixed $owners
 * @property-read mixed $plan_message
 * @property-read mixed $plan_name
 * @property-read mixed $powered_by
 * @property-read mixed $title
 * @property-read mixed $upload_max_size
 * @property-read mixed $url
 * @property-read mixed $used_ai_tokens
 * @property-read mixed $visibility_levels
 * @property-read mixed $web_url
 * @method static \think\db\Query deleting()
 */
class Space extends Model
{
    const USED_AI_TOKENS_KEY = 'space_used_ai_tokens_%u';

    protected $globalScope = ['deleting'];

    protected $type = [
        'expire_time' => Date::class,
        'block_time'  => Date::class,
        'delete_time' => Date::class,
        'features'    => 'json',
    ];

    protected $append = ['url', 'domain', 'powered_by'];

    public function activities()
    {
        return $this->hasMany(Activity::class);
    }

    public function parkedDomain()
    {
        return $this->hasOne(Domain::class);
    }

    public function books()
    {
        return $this->hasMany(Book::class);
    }

    public function owner()
    {
        return $this->morphTo();
    }

    public function accessTokens()
    {
        return $this->morphMany(AccessToken::class, 'accessible');
    }

    public function isOrg()
    {
        return $this->owner_type == Organization::class;
    }

    /**
     * 获取功能配置
     */
    public function getFeature($key, $default = null)
    {
        return ($this->features[$key] ?? null) ?: $default;
    }

    /**
     * @param \app\model\User|null $user
     * @param $type
     * @param \app\model\Book|null $book
     * @param $content
     */
    public function createActivity(?User $user, $type, ?Book $book, $content = null, $owner = null)
    {
        try {
            /** @var \app\model\Activity $activity */
            $activity = $this->activities()->save([
                'user_id' => $user?->id ?? 0,
                'type'    => $type,
                'book_id' => $book?->id,
                'content' => $content,
            ]);

            if ($owner) {
                $activity->owner()->associate($owner);
            }
        } catch (Exception) {

        }
    }

    public function createBook(User $user, $data)
    {
        $this->transaction(function () use ($user, $data) {
            /** @var Book $book */
            $book = $this->books()->save($data);

            $book->addMember($user, Member::MASTER);

            queue(InitBook::class, $book->id, queue: 'book');
            event(new BookCreated($user, $book));
        });
    }

    public function isExpired()
    {
        if (is_saas() && $this->isOrg()) {
            if (!$this->expire_time || $this->expire_time->lt(Date::now())) {
                return true;
            }
        }
        return false;
    }

    public function isBlocked()
    {
        return !!$this->block_time;
    }

    public function getExpireMessageAttr()
    {
        if (!$this->expire_time) {
            return '';
        }
        if ($this->expire_time->lt(Date::now())) {
            return '<span class="text-danger">已过期</span>';
        }

        if ($this->plan == 'trial') {
            $message = "<span>试用期至：</span>";
        } else {
            $message = "<span>有效期至：</span>";
        }

        $message .= "<span>{$this->expire_time->format('Y-m-d')}</span>";

        return $message;
    }

    protected function getMaxSizeAttr()
    {
        return $this->current_plan->size + $this->size;
    }

    protected function getMediaSizeAttr()
    {
        return $this->invoke(function (Cache $cache) {
            return $cache->remember('space_media_size_' . $this->id, function () {
                return $this->books->reduce(function ($size, Book $book) {
                    return $size + $book->lfsObjects()->sum('size');
                }, 0);
            }, 3600);
        });
    }

    protected function getMediaMaxSizeAttr()
    {
        $GB = 1024 ** 3;
        return $this->max_size * $GB;
    }

    protected function getCdnSizeAttr()
    {
        return $this->bytes;
    }

    protected function getCdnMaxSizeAttr()
    {
        return $this->media_max_size * 10;
    }

    protected function getUploadMaxSizeAttr()
    {
        return $this->media_max_size / 1024 * 10;
    }

    protected function getMembersNumAttr()
    {
        if ($this->isOrg()) {
            return $this->owner->members()->count();
        }
        return 1;
    }

    protected function getMembersMaxNumAttr()
    {
        return $this->current_plan->members;
    }

    /**
     * 创始人
     */
    protected function getOwnersAttr()
    {
        if ($this->isOrg()) {
            return $this->owner->owners;
        }
        return Collection::make([$this->owner]);
    }

    protected function getPoweredByAttr($value)
    {
        if ($this->isOrg()) {
            return $this->owner->powered_by;
        }
        return true;
    }

    protected function getAiTokensAttr()
    {
        return $this->max_ai_tokens - $this->used_ai_tokens;
    }

    protected function getUsedAiTokensAttr()
    {
        return $this->invoke(function (Cache $cache) {
            $key = sprintf(self::USED_AI_TOKENS_KEY, $this->id);
            return $cache->get($key, 0);
        });
    }

    protected function getMaxAiTokensAttr()
    {
        $tokens = $this->tokens * 1000;
        if ($this->isEnterprisePlan(true)) {
            return $tokens + 1000000;
        }
        return $tokens + 100000;
    }

    public function checkQuota($name)
    {
        if (is_saas()) {
            switch ($name) {
                case 'media':
                    return $this->media_max_size > $this->media_size;
                case 'cdn':
                    return $this->cdn_max_size > $this->cdn_size;
                case 'member':
                    return !$this->members_max_num || $this->members_max_num > $this->members_num;
            }
        }
        return true;
    }

    public function consumeAiTokens($usage)
    {
        if ($usage > 0) {
            $this->invoke(function (Cache $cache) use ($usage) {
                $key = sprintf(self::USED_AI_TOKENS_KEY, $this->id);
                if (!$cache->has($key)) {
                    $cache->set($key, $usage, Date::now()->endOfMonth());
                } else {
                    $cache->inc($key, $usage);
                }
            });
        }
    }

    protected function getModerationAttr($value)
    {
        return $this->invoke(function (Moderation $moderation) use ($value) {
            if (!$moderation->isAvailable()) {
                return -1;
            }
            return $value;
        });
    }

    public function isEnterprisePlan($strict = false)
    {
        return $this->current_plan instanceof EnterprisePlan && (!$strict || $this->current_plan->name == 'enterprise');
    }

    protected function getCurrentPlanAttr()
    {
        if ($this->isOrg()) {
            switch ($this->plan) {
                case 'trial':
                    return new TrialPlan($this);
                case 'team':
                    return new TeamPlan($this);
                case 'enterprise':
                    return new EnterprisePlan($this);
            }
        }
        return new UserPlan($this);
    }

    protected function getLogoAttr()
    {
        if ($this->isOrg()) {
            return $this->owner->getAttr('logo');
        }
        return asset('images/logo.svg');
    }

    protected function getTitleAttr()
    {
        if ($this->isOrg()) {
            return $this->owner->getAttr('name');
        }
        return '知识管理';
    }

    protected function getNameAttr()
    {
        return $this->owner->getAttr('name');
    }

    protected function getDomainAttr()
    {
        if (is_saas()) {
            return $this->hash_id . '.' . config('app.host');
        } else {
            return true;
        }
    }

    protected function getCnameAttr()
    {
        return $this->hash_id . '.k.' . config('app.cname_host');
    }

    protected function getUrlAttr()
    {
        return (string) url('/-')->domain($this->domain);
    }

    protected function getWebUrlAttr()
    {
        return (string) url('/')->domain($this->domain);
    }

    protected function getHashIdAttr()
    {
        return Hashids::encode($this->id);
    }

    protected function getVisibilityLevelsAttr()
    {
        if ($this->isOrg()) {
            return [
                0  => [
                    'name'        => '私有',
                    'description' => '仅成员可见',
                ],
                10 => [
                    'name'        => '内部',
                    'description' => '空间成员可见',
                ],
                20 => [
                    'name'        => '公开',
                    'description' => '互联网所有人可见',
                ],
            ];
        } else {
            return [
                0  => [
                    'name'        => '私有',
                    'description' => '仅自己可见',
                ],
                20 => [
                    'name'        => '公开',
                    'description' => '互联网所有人可见',
                ],
            ];
        }
    }

    public function isDeleting()
    {
        return $this->delete_time && $this->delete_time > Date::now()->subDay();
    }

    /**
     * 删除中
     * @param Query $query
     * @return void
     */
    public function scopeDeleting(Query $query)
    {
        $query->where(function (Query $query) {
            $query->whereNull('delete_time', 'OR');
            $query->whereOr('delete_time', '>', Date::now()->subDay());
        });
    }

    public function isSSOAvailable()
    {
        if ($this->isOrg()) {
            return $this->isEnterprisePlan() && $this->owner->sso && $this->owner->idp;
        }
        return false;
    }

    public function getSamlIdp()
    {
        if ($this->isOrg()) {
            return $this->owner->idp;
        }
        return null;
    }

    public function getSamlAuth()
    {
        $sp = new ServiceProvider([
            'entityId'                  => space_url($this, '/-/saml/metadata', true),
            'assertionConsumerService'  => [
                'url'     => space_url($this, '/-/saml/acs', true),
                'binding' => 'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST',
            ],
            "attributeConsumingService" => [
                "serviceName"         => "Knowledge Single Sign-On",
                "requestedAttributes" => [],
            ],
            'NameIDFormat'              => 'urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress',
            'x509cert'                  => config('saml.public_key'),
            'privateKey'                => config('saml.private_key'),
        ]);

        return new Auth($sp);
    }
}
