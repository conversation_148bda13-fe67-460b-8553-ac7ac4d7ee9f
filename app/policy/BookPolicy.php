<?php

namespace app\policy;

use app\model\Book;
use app\model\BookArticle;
use app\model\Member;
use app\model\User;

class BookPolicy extends BasePolicy
{
    /**
     * 前置检查，空间管理员拥有所有权限
     * @param User|null $user
     * @param string $ability
     * @param Book $book
     * @return bool|void
     */
    public function before(?User $user, $ability, $book)
    {
        if ($ability == 'quit') {
            return;
        }

        if ($user) {
            //系统管理员拥有所有空间的权限
            if ($user && $user->hasPermission('admin')) {
                return true;
            }

            if ($book->space->isOrg() && $book->space->owner->hasMember($user, Member::MASTER)) {
                return true;
            }
        }
    }

    public function browse(?User $user, Book $book)
    {
        if ($book->visibility_level >= Book::LEVEL_PUBLIC) {
            return true;
        }
        if (!$user) {
            return false;
        }
        if ($book->visibility_level >= Book::LEVEL_PROTECTED && $book->space->isOrg()) {
            return $book->space->owner->hasMember($user, Member::READER);
        }
        return $book->hasMember($user, Member::READER);
    }

    public function read(User $user, Book $book)
    {
        if ($book->visibility_level >= Book::LEVEL_PROTECTED && $book->space->isOrg()) {
            return $book->space->owner->hasMember($user, Member::READER);
        }
        return $book->hasMember($user, Member::READER);
    }

    public function content(?User $user, Book $book, ?BookArticle $article = null)
    {
        if ($book->is_sale) {
            if ($article && $article->trial) {
                return true;
            }
            if (!$user) {
                return false;
            }
            return $book->hasBuyer($user) || $book->hasMember($user, Member::READER);
        }
        return true;
    }

    public function write(User $user, Book $book)
    {
        return $book->hasMember($user, Member::WRITER);
    }

    public function admin(User $user, Book $book)
    {
        return $book->hasMember($user, Member::MASTER);
    }

    public function quit(User $user, Book $book)
    {
        return $book->hasMember($user) && $book->space->isOrg();
    }
}
