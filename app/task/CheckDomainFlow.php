<?php

namespace app\task;

use app\lib\Cdn;
use app\model\Domain;
use think\facade\Log;
use TopThinkCloud\Notification\Notice;
use yunwuxin\cron\Task;

class CheckDomainFlow extends Task
{
    public function configure()
    {
        $this->everyFiveMinutes()
            ->withoutOverlapping(600)
            ->onOneServer();
    }

    protected function handle(Cdn $cdn)
    {
        Domain::chunk(10, function ($domains) use ($cdn) {
            /** @var Domain $domain */
            foreach ($domains as $domain) {
                try {
                    if (empty($domain->space)) {
                        //空间已删除
                        //TODO 删除域名并关闭CDN
                        //这里的关联查询需要支持查询已删除的space并判断是否已软删除
                        continue;
                    }

                    ['bytes' => $bytes, 'dreqs' => $dreqs] = $cdn->queryFlow($domain);

                    $current  = $domain->space->bytes;
                    $maxBytes = $domain->space->cdn_max_size;

                    $domain->space->save([
                        'bytes' => $bytes,
                    ]);

                    $domain->save([
                        'bytes' => $bytes,
                        'dreqs' => $dreqs,
                    ]);

                    //超出后禁止
                    if ($bytes >= $maxBytes) {
                        if ($domain->status != 0) {
                            $cdn->disableService($domain);
                            $domain->save([
                                'status' => 0,
                            ]);
                        }
                    } else {
                        if ($domain->status != 1) {
                            $cdn->enableService($domain);
                            $domain->save([
                                'status' => 1,
                            ]);
                        }
                    }

                    //提前通知
                    $threshold = $maxBytes * 0.9;
                    if ($bytes >= $threshold && $current < $threshold) {
                        Notice::create("您的空间[{$domain->space->title}]本月CDN流量即将消耗完，请及时扩容", sms: true)
                            ->notify($domain->space->owner->owners);
                    }
                } catch (\Exception $e) {
                    Log::error($e->getMessage());
                    Log::error($e->getTraceAsString());
                    //TODO 关闭CDN
                }
            }
        });
    }
}
