<?php

namespace app\task;

use app\lib\Date;
use app\model\Organization;
use app\model\OrganizationEarnings;
use Exception;
use think\facade\Log;
use TopThinkCloud\Client;
use yunwuxin\cron\Task;

class SettleEarnings extends Task
{
    protected function configure()
    {
        $this->dailyAt('3:00')->onOneServer();
    }

    public function isDue()
    {
        if (!is_saas()) {
            return false;
        }
        return parent::isDue();
    }

    public function handle(Client $client)
    {
        $client->authenticate();

        $cursor = Organization::where('earnings', '>', 0)->order('id asc')->cursor();

        /** @var Organization $org */
        foreach ($cursor as $org) {
            try {
                $org->transaction(function () use ($client, $org) {
                    $settling = $org->earningses()
                        ->where('create_time', '>=', Date::now()->subDays(7))
                        ->where('type', OrganizationEarnings::TYPE_INC)
                        ->sum('amount');

                    $nums = $org->earnings - $settling;
                    if ($nums > 0) {
                        $org->updateEarnings(OrganizationEarnings::TYPE_DEC, $nums, '结算云币');
                        $client->user()->coin($org->founder->getOpenid())->inc($nums, '知识管理收入结算');
                    }
                });
            } catch (Exception $e) {
                Log::error($e->getMessage());
            }
        }
    }
}
