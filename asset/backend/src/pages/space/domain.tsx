import { Content, formatLongNumber, request, Table } from '@topthink/common';
import { FormCheck } from 'react-bootstrap';

export default function() {
    return <Content>
        <Table
            source={`space/domain`}
            columns={[
                {
                    title: '域名',
                    dataIndex: 'name',
                },
                {
                    title: '所属空间',
                    dataIndex: ['space', 'owner', 'name'],
                    render({ value, record }) {
                        if (!record.space) {
                            return '--';
                        }
                        return <a href={record.space.url} target={'_blank'}>{value}</a>;
                    }
                },
                {
                    title: '本月动态请求',
                    dataIndex: 'dreqs',
                    width: 100,
                    align: 'center'
                },
                {
                    title: '本月流量',
                    dataIndex: 'bytes',
                    width: 100,
                    align: 'center',
                    render({ value }) {
                        return formatLongNumber(value);
                    }
                },
                {
                    title: '静态缓存',
                    dataIndex: 'cache',
                    width: 100,
                    align: 'center',
                    render({ value, record, action }) {
                        return <FormCheck
                            checked={value}
                            type={'switch'}
                            onChange={async (e) => {
                                await request({
                                    method: 'post',
                                    url: `/space/domain/${record.id}/cache`,
                                    data: {
                                        cache: e.target.checked
                                    }
                                });

                                action.reload(true);
                            }}
                        />;
                    }
                },
                {
                    title: '状态',
                    dataIndex: 'status',
                    width: 100,
                    align: 'center',
                    render({ value }) {
                        switch (value) {
                            case 1:
                                return <span className={'text-success'}>服务中</span>;
                            case 0:
                                return <span className={'text-muted'}>已关闭</span>;
                            default:
                                return '--';
                        }
                    }
                }
            ]}
        />
    </Content>;
}
