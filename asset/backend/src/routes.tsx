import User from '@/pages/user';
import Space from '@/pages/space';
import Book from '@/pages/book';
import Activity from '@/pages/activity';
import { Navigate, Outlet, request, RouteObject, TabLayout } from '@topthink/common';
import Layout from '@/layout';
import Article from '@/pages/book/article';
import Moderation from '@/pages/book/moderation';
import Domain from './pages/space/domain';

const routes: RouteObject[] = [
    {
        element: <Layout />,
        children: [
            {
                index: true,
                element: <Navigate to='dashboard' replace />
            },
            {
                path: 'dashboard',
                lazy: () => import('@/pages/dashboard'),
                meta: {
                    title: '仪表盘',
                    icon: 'speedometer2'
                }
            },
            {
                path: 'user',
                element: <User />,
                meta: {
                    title: '用户',
                    icon: 'people'
                }
            },
            {
                path: 'space',
                element: <Outlet />,
                meta: {
                    title: '空间',
                    icon: 'boxes',
                    access: (app) => {
                        return app.saas;
                    }
                },
                children: [
                    {
                        index: true,
                        element: <Navigate to='list' replace />,
                    },
                    {
                        path: 'list',
                        element: <Space />,
                        meta: {
                            title: '空间列表',
                        }
                    },
                    {
                        path: 'domain',
                        element: <Domain />,
                        meta: {
                            title: '域名管理',
                        }
                    }
                ]
            },
            {
                path: 'book',
                element: <Outlet />,
                meta: {
                    title: '文档',
                    icon: 'journals'
                },
                children: [
                    {
                        index: true,
                        element: <Navigate to='list' replace />,
                    },
                    {
                        path: 'list',
                        element: <Book />,
                        meta: {
                            title: '文档列表',
                        }
                    },
                    {
                        path: 'moderation',
                        element: <Moderation />,
                        meta: {
                            title: '内容审查',
                        }
                    },
                    {
                        path: ':book',
                        element: <Outlet />,
                        id: 'book',
                        loader({ params: { book } }) {
                            return request(`/book/${book}`);
                        },
                        children: [
                            {
                                path: 'article',
                                element: <Article />,
                            }
                        ]
                    }
                ]
            },
            {
                path: 'activity',
                element: <Activity />,
                meta: {
                    title: '动态',
                    icon: 'activity'
                }
            },
            {
                path: 'setting',
                element: <TabLayout />,
                meta: {
                    title: '设置',
                    icon: 'gear',
                    hideChildrenInMenu: true
                },
                children: [
                    {
                        index: true,
                        element: <Navigate to='website' replace />
                    },
                    {
                        path: 'website',
                        lazy: () => import('@/pages/setting/website'),
                        meta: {
                            title: '网站设置'
                        }
                    },
                    {
                        path: 'email',
                        lazy: () => import('@/pages/setting/mail'),
                        meta: {
                            title: '邮箱设置',
                            access: (app) => {
                                return !app.saas;
                            }
                        }
                    },
                    {
                        path: 'login',
                        lazy: () => import('@/pages/setting/login'),
                        meta: {
                            title: '登录设置',
                            access: (app) => {
                                return !app.saas;
                            }
                        }
                    },
                    {
                        path: 'feature',
                        lazy: () => import('@/pages/setting/feature'),
                        meta: {
                            title: '功能设置'
                        }
                    },
                ]
            }
        ]
    }
];

export default routes;
