export default function(url: string) {

    $('[data-bs-selector="change"]').api({
        on: 'change',
        url,
        method: 'PUT',
        data: function() {
            let value;
            if ($(this).val() === 'on') {
                value = $(this).prop('checked') ? 1 : 0;
            } else {
                value = $(this).val();
            }

            return {
                field: $(this).prop('name'),
                value
            };
        }
    });

    const $payRead = $('[name="pay_read"]');

    $payRead.on('change', function() {
        const checked = $(this).is(':checked');
        $('[data-bs-selector="price"]').toggleClass('d-none', !checked);
    });

    $('[data-bs-selector="price"]').toggleClass('d-none', !$payRead.is(':checked'));
}
