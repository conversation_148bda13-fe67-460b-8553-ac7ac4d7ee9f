<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateBookSalesTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('book_sale')
            ->addColumn(Column::integer('book_id')->setComment('文档ID'))
            ->addColumn(Column::integer('user_id')->setComment('购买用户ID'))
            ->addColumn(Column::integer('amount')->setComment('支付金额(分)'))
            ->addTimestamps()
            ->addIndex(['book_id', 'user_id'], ['unique' => true])
            ->create();
    }
}
