<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateSpaceIncomeTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('organization_earnings')
            ->addColumn(Column::integer('org_id')->setComment('组织ID'))
            ->addColumn(Column::tinyInteger('type')->setComment('类型：增加/减少'))
            ->addColumn(Column::integer('amount')->setComment('额(分)'))
            ->addColumn(Column::string('info')->setComment('备注'))
            ->addNullableMorphs('source')
            ->addTimestamps()
            ->addIndex(['org_id'])
            ->create();
    }
}
