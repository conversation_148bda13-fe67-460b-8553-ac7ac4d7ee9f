<?php

use app\middleware\AjaxRedirect;
use think\facade\Route;
use think\middleware\SessionInit;
use yunwuxin\Auth;
use yunwuxin\auth\middleware\Authentication;

Route::group('admin/api', function () {

    Route::get('manifest', 'manifest/index');

    Route::group(function () {
        Route::get('current', function (Auth $auth) {
            return $auth->user();
        });

        Route::get('license', 'license/index');

        Route::group('statistic', function () {
            Route::get('basic', 'statistic/basic');
            Route::get('user', 'statistic/user');
        });

        Route::resource('user', 'user');
        Route::get('user/:id/binds', 'user/binds');

        Route::group('space', function () {
            Route::get('domain', 'domain/index');
            Route::post('domain/:id/cache', 'domain/cache');

            Route::post(':id/plan', 'index/plan');
            Route::post(':id/block', 'index/block');
            Route::post(':id/unblock', 'index/unblock');
            Route::post(':id/moderation/:action', 'index/moderation');
            Route::post(':id/preemptive/:action', 'index/preemptive');

            Route::resource('', 'index');
        })->prefix('admin.space.');

        Route::group('book', function () {
            Route::get('moderation', 'moderation/index');
            Route::post('moderation/mark', 'moderation/mark');

            Route::resource('/', 'index');

            Route::post(':id/block', 'index/block');
            Route::post(':id/unblock', 'index/unblock');
            Route::get(':book/article/:id/part', 'article/part');
            Route::get(':book/article/:id/content', 'article/content');
            Route::get(':book/article', 'article/index');
        })->prefix('admin.book.');

        Route::resource('application', 'application');

        //动态
        Route::get('activity', 'activity/index');

        //设置
        Route::get('setting/:name', 'setting/read');
        Route::post('setting/:name', 'setting/update');
    })->middleware([Authentication::class]);

    Route::miss(function () {
        abort(404);
    });
})
    ->prefix('admin.')
    ->middleware([SessionInit::class, AjaxRedirect::class]);
