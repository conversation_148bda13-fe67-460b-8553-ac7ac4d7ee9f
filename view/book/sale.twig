{% extends "book/layout.twig" %}
{% block page_title %}
    销售记录
{% endblock %}
{% block page_body %}

    {# 销售统计卡片 #}
    {% embed "components/card.twig" %}
        {% block body %}
            <div class="row text-center">
                <div class="col-md-2">
                    <div class="border-end">
                        <h5 class="text-primary mb-1">{{ statistics.total_sales }}</h5>
                        <small class="text-muted">总销售量</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="border-end">
                        <h5 class="text-success mb-1">￥{{ format_price(statistics.total_amount) }}</h5>
                        <small class="text-muted">总销售额</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="border-end">
                        <h5 class="text-info mb-1">{{ statistics.today_sales }}</h5>
                        <small class="text-muted">今日销售量</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="border-end">
                        <h5 class="text-warning mb-1">￥{{ format_price(statistics.today_amount) }}</h5>
                        <small class="text-muted">今日销售额</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="border-end">
                        <h5 class="text-secondary mb-1">{{ statistics.month_sales }}</h5>
                        <small class="text-muted">本月销售量</small>
                    </div>
                </div>
                <div class="col-md-2">
                    <div>
                        <h5 class="text-dark mb-1">￥{{ format_price(statistics.month_amount) }}</h5>
                        <small class="text-muted">本月销售额</small>
                    </div>
                </div>
            </div>
        {% endblock %}
    {% endembed %}

    {# 销售记录表格 #}
    {% embed "components/card.twig" %}
        {% block body %}
            <table class="table table-hover align-middle">
                <thead>
                <tr>
                    <th>名称</th>
                    <th>购买时间</th>
                    <th width='200'>售价</th>
                    <th class='text-end'>操作</th>
                </tr>
                </thead>
                <tbody>
                {% for sale in sales %}
                    <tr>
                        <td>
                            <span class='d-flex align-items-center'>
                                <img class='me-2 rounded-circle' src='{{ sale.user.avatar }}' height='24' width='24' />
                                {{ sale.user.name }}
                            </span>
                        </td>
                        <td>{{ sale.create_time }}</td>
                        <td>
                            ￥{{ format_price(sale.amount) }}
                        </td>
                        <td class='text-end'>
                            {% if sale.canRevoke() %}
                                <a class='text-danger' data-bs-confirm data-message='确定要撤销该订单吗？' data-method='delete'
                                   href='/-/book/{{ book.hash_id }}/sale/{{ sale.id }}'>撤销</a>
                            {% else %}
                                --
                            {% endif %}
                        </td>
                    </tr>
                {% else %}
                    <tr>
                        <td class='text-center text-muted' colspan='5'>暂无记录</td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        {% endblock %}
    {% endembed %}
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import("book/sale");
    </script>
{% endblock %}
