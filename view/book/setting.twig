{% extends "book/layout.twig" %}
{% block page_title %}
    通用设置
{% endblock %}
{% block page_body %}
    {% embed "components/card.twig" %}
        {% block body %}
            <h5>可见范围</h5>
            <hr />
            {% for key,level in book.space.visibility_levels %}
                <div class="form-check">
                    <input data-bs-selector='change' class="form-check-input" type="radio"
                           name="visibility_level"
                           id="visibility_level_{{ key }}"
                           value="{{ key }}" {{ key == book.visibility_level?'checked' }} />
                    <label class="form-check-label"
                           for="visibility_level_{{ key }}">
                        {{ level.name }}
                    </label>
                    <div class="mb-2 form-text text-muted">
                        {{ level.description }}
                    </div>
                </div>
            {% endfor %}
        {% endblock %}
    {% endembed %}
    {% embed "components/card.twig" %}
        {% block body %}
            <h5>功能设置</h5>
            <hr />
            <div class="form-check form-switch mb-3">
                <input data-bs-selector='change' class="form-check-input" type="checkbox"
                       name='is_template' {{ book.is_template?'checked' }}
                       id="is_template" />
                <label class="form-check-label" for="is_template">模板文档</label>
                <div class="form-text">
                    模板文档允许用户快速生成具有相同目录结构和文件的新文档。
                </div>
            </div>
            <div class="form-check form-switch  mb-3">
                <input data-bs-selector='change' class="form-check-input" type="checkbox"
                       name='token_read' {{ book.token_read?'checked' }}
                       id="token_read" />
                <label class="form-check-label" for="token_read">令牌阅读</label>
                <div class="form-text">
                    私有或内部文档允许用户通过文档令牌获得阅读权限。
                </div>
            </div>
            {% if book.space.isOrg() %}
                {% if is_saas() %}
                    <div class="form-check form-switch mb-3">
                        <input data-bs-selector='change' class="form-check-input" type="checkbox" name='pay_read' {{ book.pay_read?'checked' }} id="pay_read" />
                        <label class="form-check-label" for="pay_read">付费阅读</label>
                        <div class="form-text mb-2">
                            公开文档允许用户付费后阅读。
                        </div>
                        <div data-bs-selector='price' class='d-inline-flex d-none'>
                            <form action='/-/book/{{ book.hash_id }}/price' data-bs-form method='post'>
                                <div class="input-group input-group-sm mb-3">
                                    <span class="input-group-text">￥</span>
                                    <input type="number" name='price' class="form-control" value='{{ book.price }}' placeholder='价格' min='0' step='0.01' />
                                    <button class="btn btn-primary" type="submit">保存</button>
                                </div>
                            </form>
                        </div>
                    </div>
                {% endif %}
                <div class="form-check form-switch  mb-3">
                    <input data-bs-selector='change' class="form-check-input" type="checkbox" name='ask' {{ book.ask?'checked' }} id="ask" />
                    <label class="form-check-label" for="ask">智问搜索</label>
                    <div class="form-text">
                        开启后文档发布时将自动将文档内容投入到AI系统里进行训练，用户可以通过智问搜索的方式直接获取问题的答案。
                    </div>
                </div>
                <div class="form-check form-switch">
                    <input data-bs-selector='change' class="form-check-input" type="checkbox" name='write_release' {{ book.write_release?'checked' }} id="write_release" {{ not space.isEnterprisePlan() ?'disabled' }} />
                    <label class="form-check-label" for="write_release">允许"写作者"发布文档</label>
                    {% if not space.isEnterprisePlan() %}
                        <a class="badge bg-danger link-light" data-bs-toggle="tooltip" data-bs-placement="bottom"
                           title="此功能仅企业版可用"
                           href='/-/org/billing'>升级</a>
                    {% endif %}
                    <div class="form-text">
                        关闭后仅管理员可发布此文档。
                    </div>
                </div>
            {% endif %}
        {% endblock %}
    {% endembed %}
    {% if book.space.isOrg() %}
        {% embed "components/card.twig" %}
            {% block body %}
                <h5>自动打包设置</h5>
                <hr />
                <div class="form-text mb-3">
                    发布时自动打包电子书，开启后将影响发布效率，按需开启
                </div>
                <div class='row'>
                    <div class="col">
                        <div class="form-check form-switch">
                            <input data-bs-selector='change' class="form-check-input" type="checkbox"
                                   name='pdf' {{ 'pdf' in book.pack_types?'checked' }}
                                   id="pack_pdf" />
                            <label class="form-check-label" for="pack_pdf">PDF</label>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-check form-switch">
                            <input data-bs-selector='change' class="form-check-input" type="checkbox"
                                   name='epub' {{ 'epub' in book.pack_types?'checked' }}
                                   id="pack_epub" />
                            <label class="form-check-label" for="pack_epub">ePub</label>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-check form-switch">
                            <input data-bs-selector='change' class="form-check-input" type="checkbox"
                                   name='word' {{ 'word' in book.pack_types?'checked' }}
                                   id="pack_word" />
                            <label class="form-check-label" for="pack_word">Word</label>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-check form-switch">
                            <input data-bs-selector='change' class="form-check-input" type="checkbox"
                                   name='html' {{ 'html' in book.pack_types?'checked' }}
                                   id="pack_html" />
                            <label class="form-check-label" for="pack_html">HTML</label>
                        </div>
                    </div>
                </div>
            {% endblock %}
        {% endembed %}
    {% endif %}
    {% embed "components/card.twig" %}
        {% block title %}
            阅读路径
        {% endblock %}
        {% block body %}
            <form action='/-/book/{{ book.hash_id }}/slug' data-bs-form method='post'>
                <div class="input-group mb-3">
                    <span class="input-group-text">{{ space.web_url }}@</span>
                    <input type="text" name='slug' class="form-control" placeholder='{{ book.hash_id }}'
                           value='{{ book.slug ?: book.hash_id }}' />
                </div>
                <p class="text-muted">
                    此路径的更改同时会影响到文档被关联时的阅读路径
                </p>
                <button type='submit' class='btn btn-primary'>保存</button>
            </form>
        {% endblock %}
    {% endembed %}
    {% if can(user,'quit',book) %}
        {% embed "components/card.twig" %}
            {% block body %}
                <h5>退出文档</h5>
                <hr />
                <p class='text-muted'>退出文档后会失去该文档的所有权限，请谨慎操作</p>
                <a data-bs-confirm data-message='确定要退出吗？' href='/-/book/{{ book.hash_id }}/quit'
                   data-method='post'
                   class='btn btn-outline-danger'>退出</a>
            {% endblock %}
        {% endembed %}
    {% endif %}
    {% embed "components/card.twig" %}
        {% block body %}
            <h5>删除文档</h5>
            <hr />
            <p class='text-muted'>删除文档会删除文档的所有数据，请谨慎操作</p>
            <a data-bs-confirm data-message='确定要删除吗？' href='/-/book/{{ book.hash_id }}' data-method='delete'
               class='btn btn-outline-danger'>删除</a>
        {% endblock %}
    {% endembed %}
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import("book/info", "/-/book/{{ book.hash_id }}");
    </script>
{% endblock %}
