<header>
    <nav class="navbar bg-white border-bottom fixed-top px-3">
        <div class="container-fluid">
            {% block logo %}
                <div class='logo d-flex align-items-center'>
                    <a class="navbar-brand d-flex align-items-center" href="/-">
                        <img class='me-3 rounded' src="{{ space.logo }}" width='25' height='25' />
                        {{ space.title }}
                    </a>
                    {% if is_saas() and user is instance of("\\app\\model\\User") and not request.isParked() %}
                        <div class="dropdown">
                            <a class="nav-link no-caret dropdown-toggle" type='button' data-bs-toggle="dropdown">
                                <i class="bi bi-chevron-down"></i>
                            </a>
                            <ul class="dropdown-menu dropdown-menu shadow">
                                {% if user.space %}
                                    <li><h6 class="dropdown-header">个人</h6></li>
                                    <li>
                                        <a class="dropdown-item d-flex align-items-center justify-content-between"
                                           href="{{ user.space.url }}">
                                            <span class='d-flex align-items-center'>
                                                <img class='me-2 rounded' src="{{ user.avatar }}" width='25' height='25' />
                                                {{ user.name }}
                                            </span>
                                            {% if space.id==user.space.id %}
                                                <i class="bi bi-check-lg text-success"></i>
                                            {% endif %}
                                        </a>
                                    </li>
                                {% endif %}
                                {% set orgs = user.orgs.all|filter(o=>o.space) %}
                                {% if count(orgs)>0 %}
                                    <li><h6 class="dropdown-header">空间</h6></li>
                                    {% for org in orgs %}
                                        <li>
                                            <a class="dropdown-item d-flex align-items-center justify-content-between"
                                               href="{{ org.space.url }}">
                                                <span class='d-flex align-items-center'>
                                                    <img class='me-2 rounded' src="{{ org.logo }}" width='25'
                                                         height='25' />
                                                    {{ org.name }}
                                                    {% if org.space.isBlocked() %}
                                                        <span class="badge bg-danger ms-2">已禁用</span>
                                                    {% endif %}
                                                </span>
                                                {% if space.id==org.space.id %}
                                                    <i class="bi bi-check-lg text-success"></i>
                                                {% endif %}
                                            </a>
                                        </li>
                                    {% endfor %}
                                {% endif %}
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <li>
                                    <a class="dropdown-item" href="/-/org/new">
                                        <i class="me-2 bi bi-plus-square"></i>创建空间
                                    </a>
                                </li>
                            </ul>
                        </div>
                    {% endif %}
                </div>
            {% endblock %}
            <div class='flex-fill'>
                {% block nav %}
                {% endblock %}
            </div>
            <div class="d-flex align-items-center">
                <ul class='navbar-nav flex-row me-3'>
                    {% for nav in parse_navs(setting('website.header')) %}
                        <li class='nav-item mx-2'>
                            <a class="nav-link px-2" href='{{ nav.url }}' {{ nav.target?"target='#{nav.target}'"|raw }}>
                                {{ nav.title }}
                            </a>
                        </li>
                    {% endfor %}
                </ul>
                {% if is_saas() and user is instance of("\\app\\model\\User") %}
                    <a class="nav-link px-3" href='#' data-notification>
                        <span class='position-relative'>
                            <i class="bi bi-bell"></i>
                            <span data-badge hidden class="position-absolute top-0 start-100 translate-middle p-1 bg-danger border border-light rounded-circle">
                                <span class="visually-hidden">New alerts</span>
                            </span>
                        </span>
                    </a>
                    {{ user.cloud.notification_script|raw }}
                {% endif %}
                {% if space is defined %}
                    <div class="dropdown">
                        <a class="nav-link px-3 dropdown-toggle no-caret" href='#' data-bs-toggle="dropdown">
                            <i class="bi bi-plus-circle-fill text-primary"></i>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow">
                            {% if can(user,'create',space) %}
                                <li>
                                    <a class="dropdown-item" href="/-/book/new">
                                        <i class="bi bi-journals me-2"></i>创建文档
                                    </a>
                                </li>
                            {% endif %}
                            {% if is_saas() and not request.isParked() %}
                                <li>
                                    <a class="dropdown-item" href="/-/org/new">
                                        <i class="bi bi-boxes me-2"></i>创建空间
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </div>
                {% endif %}
                <div class="dropdown ms-3">
                    <a class="nav-link dropdown-toggle d-flex align-items-center pe-0" href='#' data-bs-toggle="dropdown">
                        <img class='rounded-circle' src="{{ user.avatar }}" width='24' height='24' />
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end shadow">
                        <li>
                            <a class="dropdown-item" href="/-/user/setting">账户设置</a>
                        </li>
                        {% if is_saas() and user is instance of("\\app\\model\\User") %}
                            <li>
                                <a class="dropdown-item" target='_blank' href="{{ user.cloud.finance_url }}">
                                    费用中心
                                </a>
                            </li>
                        {% endif %}
                        {% if space is defined and space.isOrg() %}
                            {% if space.owner.index is not same as('workspace') %}
                                <li>
                                    <a class="dropdown-item" href="/" target='_blank'>空间主页</a>
                                </li>
                            {% endif %}
                            {% if can(user,'admin',space) %}
                                <li>
                                    <a class="dropdown-item" href="/-/org/setting">空间设置</a>
                                </li>
                            {% endif %}
                        {% endif %}
                        {% if can(user,'admin') %}
                            <li>
                                <a class="dropdown-item" target='_blank' href="{{ main_url('/admin') }}">
                                    系统设置
                                </a>
                            </li>
                        {% endif %}
                        <li>
                            <hr class="dropdown-divider">
                        </li>
                        <li>
                            <a class="dropdown-item" href="/-/user/logout">退出登录</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>
</header>
