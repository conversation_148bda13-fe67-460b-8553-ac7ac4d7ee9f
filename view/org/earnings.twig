{% extends "org/layout.twig" %}
{% block web_title %}
    收入管理
{% endblock %}
{% block page_title %}
    收入管理
{% endblock %}
{% block page_body %}
    {# 账户与余额信息卡片 #}
    {% embed "components/card.twig" %}
        {% block header %}
            <i class="bi bi-wallet2 me-2"></i>结算信息
        {% endblock %}
        {% block body %}
            <div class="row">
                {# 结算账户信息 #}
                <div class="col-md-5">
                    <div class="d-flex align-items-center h-100">
                        <div class="me-3">
                            <img src="{{ founder ? founder.avatar : 'https://via.placeholder.com/50x50/007bff/ffffff?text=U' }}"
                                 alt="创始人头像"
                                 class="rounded-circle"
                                 width="50"
                                 height="50">
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ founder ? founder.name : '创始人用户名' }}</h6>
                            <small class="text-muted">企业创始人</small>
                        </div>
                    </div>
                </div>

                {# 当前余额 #}
                <div class="col-md-4">
                    <div class="text-center p-3 bg-light rounded h-100 d-flex flex-column justify-content-center">
                        <h3 class="text-primary mb-1">￥{{ format_price(statistics.balance) }}</h3>
                        <p class="text-muted mb-0 small">当前余额</p>
                    </div>
                </div>

                {# 结算信息与操作 #}
                <div class="col-md-3">
                    <div class="h-100 d-flex flex-column justify-content-center">
                        <div class="mb-2">
                            <i class="bi bi-clock text-warning me-2"></i>
                            <small><strong>结算周期：</strong>T+7</small>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">系统将自动结算到云币</small>
                        </div>
                        <div class="d-grid gap-2">
                            <a href='{{ user.cloud.finance_url }}' target='_blank' class="btn btn-outline-primary btn-sm">
                                去提现
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        {% endblock %}
    {% endembed %}

    {# 收入统计卡片 #}
    {% embed "components/card.twig" %}
        {% block header %}
            <i class="bi bi-graph-up me-2"></i>收入统计
        {% endblock %}
        {% block body %}
            <div class="row text-center">
                <div class="col-md-4">
                    <div class="border-end">
                        <h5 class="text-success mb-1">￥{{ format_price(statistics.monthly_income) }}</h5>
                        <small class="text-muted">本月收入</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="border-end">
                        <h5 class="text-info mb-1">￥{{ format_price(statistics.total_income) }}</h5>
                        <small class="text-muted">累计收入</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div>
                        <h5 class="text-warning mb-1">￥{{ format_price(statistics.total_settled) }}</h5>
                        <small class="text-muted">已结算</small>
                    </div>
                </div>
            </div>
        {% endblock %}
    {% endembed %}

    {# 收入明细卡片 #}
    {% embed "components/card.twig" %}
        {% block header %}
            <i class="bi bi-list-ul me-2"></i>收入明细
        {% endblock %}
        {% block body %}
            <table class="table table-hover align-middle mb-0">
                <thead class="table-light">
                <tr>
                    <th width='150'>时间</th>
                    <th>描述</th>
                    <th width='100' class="text-end">金额</th>
                </tr>
                </thead>
                <tbody>
                {% for earnings in earningses %}
                    <tr>
                        <td>
                            <small class="text-muted">{{ earnings.create_time }}</small>
                        </td>
                        <td>{{ earnings.info }}</td>
                        <td class="text-end">
                            {% if earnings.type == 1 %}
                                <span class='text-success fw-bold'>+ ￥{{ format_price(earnings.amount) }}</span>
                            {% else %}
                                <span class='text-danger fw-bold'>- ￥{{ format_price(earnings.amount) }}</span>
                            {% endif %}
                        </td>
                    </tr>
                {% else %}
                    <tr>
                        <td colspan='3' class='text-muted text-center py-4'>
                            <i class="bi bi-inbox display-6 text-muted d-block mb-2"></i>
                            暂无收入记录
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>

        {% endblock %}
    {% endembed %}
    {{ earningses.render()|raw }}
{% endblock %}

{% block scripts %}
    <script type='text/javascript'>

    </script>
{% endblock %}
