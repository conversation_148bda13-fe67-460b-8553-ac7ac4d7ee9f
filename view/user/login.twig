{% extends "user/layout.twig" %}
{% block container %}
    {% if is_saas() %}
        {% if request.isParked() %}
            {% embed "components/card.twig" %}
                {% block body %}
                    <div data-bs-target='cloud'></div>
                    <script src='{{ config('cloud.host') }}/passport/js' data-target='[data-bs-target="cloud"]' data-client-id='{{ config('cloud.client_id') }}' async></script>
                {% endblock %}
            {% endembed %}
        {% else %}
            {% embed "components/card.twig" %}
                {% block body %}
                    <div class='p-3'>
                        <div class="d-grid">
                            <button data-bs-login data-method='post' data-url='/-/user/login' class="btn btn-outline-primary">
                                使用 顶想云 登录
                            </button>
                        </div>
                    </div>
                {% endblock %}
            {% endembed %}
        {% endif %}
    {% else %}
        {% if setting('login.email.enable') %}
            <div data-bs-target='email' hidden>
                {% embed "components/card.twig" %}
                    {% block body %}
                        <div class='p-3'>
                            <a data-bs-switcher class='switcher' href='#qrcode' hidden><i class='bi bi-qr-code'></i></a>
                            <form data-bs-form action='/-/user/login' method='post'>
                                <div class="mb-3">
                                    <label class="form-label">邮箱</label>
                                    <input name='username' type="text" class="form-control" />
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">密码</label>
                                    <input name='password' type="password" class="form-control" />
                                </div>
                                <div class='row mb-3'>
                                    <div class='col'>
                                        <div class="form-check">
                                            <input type="checkbox" name='remember' class="form-check-input">
                                            <label class="form-check-label">记住我</label>
                                        </div>
                                    </div>
                                    <div class='col text-end'><a href='/-/user/password'>忘记密码</a></div>
                                </div>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">登录</button>
                                </div>
                            </form>
                        </div>
                    {% endblock %}
                {% endembed %}
                {% if setting('login.register') %}
                    <p class='mt-2'>还没有账号? <a href='/-/user/register'>注册</a></p>
                {% endif %}
            </div>
        {% endif %}
        {% if setting('login.qrcode') %}
            <div data-bs-target='qrcode' hidden>
                {% embed "components/card.twig" %}
                    {% block body %}
                        <div class='p-3'>
                            <a data-bs-switcher class='switcher' href='#email' hidden><i class='bi bi-display'></i></a>
                            <div class='qr-image' data-bs-qrcode>
                                <div class="spinner-border text-primary"></div>
                            </div>
                            <h5 class='text-center'>请使用微信，扫码登录</h5>
                        </div>
                    {% endblock %}
                {% endembed %}
            </div>
        {% endif %}
    {% endif %}
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import("user/login", "{{ from }}", "{{ state }}");
    </script>
{% endblock %}
