{% extends "workspace/layout.twig" %}
{% block page_title %}
    文档
{% endblock %}
{% block page_nav %}
    {% if space.isOrg() %}
        <ul class="nav flex-fill ms-4">
            <li class="nav-item">
                <a class="{{ classnames('nav-link',{active:not request.has('scope')}) }}"
                   href="/-/workspace/book">全部文档</a>
            </li>
            <li class="nav-item">
                <a class="{{ classnames('nav-link',{active:request.param('scope')=='managed'}) }}"
                   href="/-/workspace/book/managed">我管理的</a>
            </li>
            <li class="nav-item">
                <a class="{{ classnames('nav-link',{active:request.param('scope')=='joined'}) }}"
                   href="/-/workspace/book/joined">我参与的</a>
            </li>
        </ul>
    {% endif %}
{% endblock %}
{% block page_action %}
    {% if can(user,'create',space) %}
        <a class="btn btn-primary" href='/-/book/new'>
            <i class="bi bi-plus"></i> 创建文档
        </a>
    {% endif %}
{% endblock %}
{% block page_content %}
    {% embed "components/table.twig" %}
        {% block toolbar_right %}
            <form>
                {% if request.has('group') %}
                    <input type="hidden" name="group" value="{{ request.param('group') }}" />
                {% endif %}
                <input type='text' name='name' value='{{ request.param('name') }}' class='form-control'
                       placeholder='搜索文档' />
            </form>
        {% endblock %}
        {% block thead %}
            <tr>
                <th></th>
                <th colspan='2'>名称</th>
                <th class='text-center'>最近发布</th>
                <th class='text-center'>最近更新</th>
                <th class='text-end'>操作</th>
            </tr>
        {% endblock %}
        {% block tbody %}
            {% from 'macro/book.twig' import visibility %}
            {% from 'macro/book.twig' import download %}
            {% for book in books %}
                <tr>
                    <td width='10'>
                        {{ visibility(book.visibility_level) }}
                    </td>
                    <td width='30'>
                        <img class='rounded' src='{{ book.logo }}' width='30' height='30' />
                    </td>
                    <td>
                        <a href="/-/book/{{ book.hash_id }}/dashboard">
                            {{ book.name }}
                        </a>
                        {% if book.is_template %}
                            <span data-bs-toggle="tooltip" title="模板文档">
                                <i class="bi bi-subtract text-info"></i>
                            </span>
                        {% endif %}
                        {% if book.is_sale %}
                            <span data-bs-toggle="tooltip" title="付费文档">
                                <i class="bi bi-gem text-orange"></i>
                            </span>
                        {% endif %}
                        {% if book.isBlocked() %}
                            <span class="badge bg-danger">已禁用</span>
                        {% endif %}
                    </td>
                    <td class="text-black-50 text-center" width='150'>
                        {{ book.release_time?book.release_time.diffForHumans():'--' }}
                    </td>
                    <td class="text-black-50 text-center" width='150'>{{ book.update_time.diffForHumans() }}</td>
                    <td class='text-end' width='120'>
                        <div class='d-flex justify-content-end align-items-center'>
                            {% if book.release_time %}
                                <a data-bs-toggle="tooltip" title="阅读"
                                   class='btn btn-outline-secondary border-0'
                                   href="/@{{ book.hash_id }}" target='_blank'>
                                    <i class="bi bi-eye"></i>
                                </a>
                            {% endif %}
                            {% if can(user,'write',book) %}
                                <a data-bs-toggle="tooltip" title="编辑"
                                   class='btn btn-outline-secondary border-0'
                                   href='/-/book/{{ book.hash_id }}/edit' target='_blank'>
                                    <i class="bi bi-pencil-square"></i>
                                </a>
                            {% endif %}
                            <div class="dropdown">
                                <a class="dropdown-toggle no-caret btn btn-outline-secondary border-0"
                                   type="button"
                                   data-bs-toggle="dropdown">
                                    <i class="bi bi-three-dots-vertical"></i>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end shadow" data-empty='无更多操作'>
                                    <li>
                                        <a class="dropdown-item">下载</a>
                                        <ul class='dropdown-menu shadow submenu submenu-left'>
                                            {{ download(book,'pdf',"PDF") }}
                                            {{ download(book,'epub',"ePub") }}
                                            {{ download(book,'word',"Word") }}
                                            {{ download(book,'html',"HTML") }}
                                        </ul>
                                    </li>
                                    {% if can(user,'admin',book) %}
                                        <li>
                                            <a class="dropdown-item"
                                               href="/-/book/{{ book.hash_id }}/setting">设置</a>
                                        </li>
                                    {% endif %}
                                    {% if book.is_template and can(user,'create',space) %}
                                        <li>
                                            <a class="dropdown-item"
                                               href="/-/book/new#template#{{ book.id }}">使用此模板</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </div>
                        </div>
                    </td>
                </tr>
            {% else %}
                <tr>
                    <td class='text-muted text-center' colspan='7'>暂无文档</td>
                </tr>
            {% endfor %}
        {% endblock %}
    {% endembed %}
    {{ books.render()|raw }}
{% endblock %}
