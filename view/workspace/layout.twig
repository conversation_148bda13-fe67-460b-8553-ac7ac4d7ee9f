{% extends "layout.twig" %}
{% set page_class = 'workspace-page' %}
{% block aside %}
    <nav data-bs-sidebar class='sidebar'>
        <div class='header'>工作台</div>
        <ul>
            <li>
                <a href='/-/workspace/book'><i class="bi bi-journals"></i>文档</a>
            </li>
            {% if can(user,'read',space) %}
                <li>
                    <a href='/-/workspace/activity'><i class="bi bi-activity"></i>动态</a>
                </li>
                <li>
                    <a href='/-/workspace/recycle'><i class="bi bi-trash"></i>回收站</a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% if is_saas() %}
        <div class='org-info border-top'>
            {% set plan = space.current_plan %}
            <div class='basic'>
                <div class='title'>
                    <span class='name'>{{ space.name }}</span>
                    <span class="badge bg-success">{{ plan.title }}</span>
                </div>
            </div>
            {% if space.isOrg() %}
                {% if is_saas() %}
                    <div class='d-flex align-items-center mb-3'>
                        {{ space.expire_message|raw }}
                        {% if is_saas() and can(user,'admin',space) %}
                            {% if space.plan == 'trial' %}
                                <a href='/-/org/billing/plans' class='link-primary ms-2'>购买</a>
                            {% else %}
                                <a href='/-/org/billing' class='link-primary ms-2'>续费</a>
                            {% endif %}
                        {% endif %}
                    </div>
                {% endif %}
                <div class='d-flex align-items-center mb-3'>
                    <span>空间成员：</span>
                    {% if space.members_max_num %}
                        <span>{{ space.members_num }}/{{ space.members_max_num }} 人</span>
                    {% else %}
                        <span>{{ space.members_num }} 人</span>
                    {% endif %}
                    {% if can(user,'admin',space) %}
                        <a href='/-/org/member' class='link-primary ms-2'>邀请成员</a>
                    {% endif %}
                </div>
            {% else %}
                <div class='alert alert-info'>升级空间支持成员协作、绑定域名及企业商用 <a class='ms-2' data-tas-doc='maximum' href='https://doc.topthink.com/knowledge/price.html' target='_blank'>
                        <i class="bi bi-question-circle-fill"></i>
                    </a></div>
                <div class='d-flex align-items-center mb-3'>
                    <a href='/-/org/upgrade' class='btn btn-primary'>升级空间</a>
                </div>
            {% endif %}
            {% if space.media_max_size is not null %}
                <div class='mb-2'>媒体空间：</div>
                <div class='mb-2'>
                    {{ format_bytes(space.media_size) }} / {{ format_bytes(space.media_max_size) }}
                    {% if space.isOrg() and is_saas() and can(user,'admin',space) and space.plan != 'trial' %}
                        <a href='/-/org/billing' class='link-primary ms-2'>扩容</a>
                    {% endif %}
                </div>
                <div class="progress mb-3">
                    <div class="progress-bar"
                         style='width:{{ space.media_size/space.media_max_size*100 }}%'></div>
                </div>
            {% endif %}
            {% if space.isOrg() %}
                <div class='mb-2'>管理员：</div>
                <div class='mb-3'>
                    {% for master in space.owner.masters|slice(0, 3) %}
                        <span data-bs-toggle="tooltip" data-bs-placement="right" title="{{ master.name }}">
                            <img
                                class='rounded-circle'
                                width='24'
                                height='24'
                                src='{{ master.avatar }}'
                            />
                        </span>
                    {% endfor %}
                </div>
            {% endif %}
        </div>
    {% endif %}
{% endblock %}
